import React from 'react';
import Link from 'next/link';
import Layout from '@/components/Layout';
import QuoteCalculator from '@/components/QuoteCalculator';
import ServicesCarousel from '@/components/ServicesCarousel';
import Testimonials from '@/components/Testimonials';
import WorldMap from '@/components/WorldMap';

const HomePage: React.FC = () => {
  return (
    <Layout
      title="VayuVector - Your Global Relocation Partner"
      description="Professional international relocation services. Moving lives, not just belongings. Door-to-door service with comprehensive support for expats and professionals worldwide."
    >
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-900 via-primary-800 to-secondary-600 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-hero-pattern"></div>
        </div>
        
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-secondary-500/20 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent-500/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '1s' }}></div>
        </div>

        <div className="relative z-10 container-custom text-center text-white">
          <div className="max-w-4xl mx-auto">
            {/* Main Headline */}
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 animate-fade-in-up">
              Your Global
              <span className="block text-secondary-500">
                Relocation Partner
              </span>
            </h1>
            
            {/* Tagline */}
            <p className="text-xl md:text-2xl lg:text-3xl mb-8 text-gray-200 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              Moving Lives, Not Just Belongings
            </p>
            
            {/* Description */}
            <p className="text-lg md:text-xl mb-12 text-gray-300 max-w-3xl mx-auto animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
              Professional door-to-door relocation services for international professionals. 
              From packing to destination setup, we handle every detail of your global move.
            </p>
            
            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
              <Link href="/quote" className="btn-secondary text-lg px-8 py-4 shadow-glow-accent hover:shadow-glow-accent text-black font-semibold">
                Get Free Quote
              </Link>
              <Link href="/how-it-works" className="btn-outline text-lg px-8 py-4 border-secondary-500 text-secondary-500 hover:bg-secondary-500 hover:text-black">
                How It Works
              </Link>
            </div>
            
            {/* Trust Indicators */}
            <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-secondary-400 mb-2">15+</div>
                <div className="text-gray-300">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-secondary-400 mb-2">50K+</div>
                <div className="text-gray-300">Successful Moves</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-secondary-400 mb-2">120+</div>
                <div className="text-gray-300">Countries Served</div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Quick Quote Section */}
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Get Your Moving Quote in Minutes
            </h2>
            <p className="text-lg text-gray-600">
              Tell us about your move and get an instant estimate
            </p>
          </div>

          <QuoteCalculator />
        </div>
      </section>

      {/* Services Carousel */}
      <ServicesCarousel />

      {/* World Map */}
      <WorldMap />

      {/* Testimonials */}
      <Testimonials />
    </Layout>
  );
};

export default HomePage;
