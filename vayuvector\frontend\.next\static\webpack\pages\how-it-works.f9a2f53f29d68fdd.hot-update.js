"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/how-it-works",{

/***/ "./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,PhoneIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=Bars3Icon,PhoneIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_heroicons_react_20_solid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon!=!@heroicons/react/20/solid */ \"__barrel_optimize__?names=ChevronDownIcon!=!./node_modules/@heroicons/react/20/solid/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Home\",\n        href: \"/\"\n    },\n    {\n        name: \"Services\",\n        href: \"/services\",\n        children: [\n            {\n                name: \"Residential Relocation\",\n                href: \"/services/residential\"\n            },\n            {\n                name: \"Vehicle Transportation\",\n                href: \"/services/vehicle\"\n            },\n            {\n                name: \"Documentation Support\",\n                href: \"/services/documentation\"\n            }\n        ]\n    },\n    {\n        name: \"How It Works\",\n        href: \"/how-it-works\"\n    },\n    {\n        name: \"About\",\n        href: \"/about\"\n    },\n    {\n        name: \"Contact\",\n        href: \"/contact\"\n    }\n];\nconst Header = ()=>{\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [servicesOpen, setServicesOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const isScrolled = window.scrollY > 10;\n            setScrolled(isScrolled);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-300 \".concat(scrolled ? \"bg-white/95 backdrop-blur-md shadow-soft\" : \"bg-transparent\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    src: \"/logo.svg\",\n                                    alt: \"VayuVector Logo\",\n                                    width: 200,\n                                    height: 60,\n                                    className: \"h-12 lg:h-14 w-auto\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: item.children ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: ()=>setServicesOpen(true),\n                                        onMouseLeave: ()=>setServicesOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-1 px-3 py-2 text-sm font-medium transition-colors duration-200 \".concat(isActive(item.href) ? \"text-secondary-600\" : scrolled ? \"text-gray-700 hover:text-secondary-600\" : \"text-white hover:text-secondary-200\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_heroicons_react_20_solid__WEBPACK_IMPORTED_MODULE_5__.ChevronDownIcon, {\n                                                        className: \"h-4 w-4 transition-transform duration-200 \".concat(servicesOpen ? \"rotate-180\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            servicesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-medium border border-gray-100 py-2\",\n                                                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: child.href,\n                                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-secondary-600 transition-colors duration-200\",\n                                                        children: child.name\n                                                    }, child.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 27\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: \"px-3 py-2 text-sm font-medium transition-colors duration-200 \".concat(isActive(item.href) ? \"text-secondary-600\" : scrolled ? \"text-gray-700 hover:text-secondary-600\" : \"text-white hover:text-secondary-200\"),\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"tel:******-VAYU-VEC\",\n                                    className: \"flex items-center space-x-2 px-3 py-2 text-sm font-medium transition-colors duration-200 \".concat(scrolled ? \"text-gray-700 hover:text-secondary-600\" : \"text-white hover:text-secondary-200\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PhoneIcon, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"******-VAYU-VEC\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/quote\",\n                                    className: \"btn-secondary text-black font-semibold\",\n                                    children: \"Get Quote\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"p-2 rounded-md transition-colors duration-200 \".concat(scrolled ? \"text-gray-700 hover:text-secondary-600\" : \"text-white hover:text-secondary-200\"),\n                                onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open main menu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.XMarkIcon, {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.Bars3Icon, {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined),\n                mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 bg-white rounded-lg shadow-medium mt-2\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: \"block px-3 py-2 text-base font-medium rounded-md transition-colors duration-200 \".concat(isActive(item.href) ? \"text-secondary-600 bg-secondary-50\" : \"text-gray-700 hover:text-secondary-600 hover:bg-gray-50\"),\n                                            onClick: ()=>setMobileMenuOpen(false),\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 space-y-1\",\n                                            children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: child.href,\n                                                    className: \"block px-3 py-2 text-sm text-gray-600 hover:text-secondary-600 hover:bg-gray-50 rounded-md transition-colors duration-200\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: child.name\n                                                }, child.name, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 border-t border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"tel:******-VAYU-VEC\",\n                                        className: \"flex items-center space-x-2 px-3 py-2 text-base font-medium text-gray-700 hover:text-secondary-600 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PhoneIcon, {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"******-VAYU-VEC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/quote\",\n                                        className: \"block w-full mt-2 btn-primary text-center\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Get Quote\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Header, \"gFMnf2dRD7/9KwAbeSTM/4qKteo=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Header;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Header.tsx\n"));

/***/ })

});