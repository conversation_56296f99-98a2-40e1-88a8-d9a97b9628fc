/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=ArchiveBoxIcon,DocumentTextIcon,GlobeAltIcon,HomeIcon,ShieldCheckIcon,TruckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArchiveBoxIcon,DocumentTextIcon,GlobeAltIcon,HomeIcon,ShieldCheckIcon,TruckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArchiveBoxIcon: () => (/* reexport safe */ _ArchiveBoxIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   DocumentTextIcon: () => (/* reexport safe */ _DocumentTextIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   GlobeAltIcon: () => (/* reexport safe */ _GlobeAltIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   HomeIcon: () => (/* reexport safe */ _HomeIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   ShieldCheckIcon: () => (/* reexport safe */ _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   TruckIcon: () => (/* reexport safe */ _TruckIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArchiveBoxIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArchiveBoxIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArchiveBoxIcon.js\");\n/* harmony import */ var _DocumentTextIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DocumentTextIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _GlobeAltIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GlobeAltIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _HomeIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./HomeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ShieldCheckIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _TruckIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TruckIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/TruckIcon.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcmNoaXZlQm94SWNvbixEb2N1bWVudFRleHRJY29uLEdsb2JlQWx0SWNvbixIb21lSWNvbixTaGllbGRDaGVja0ljb24sVHJ1Y2tJY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQytEO0FBQ0k7QUFDUjtBQUNSO0FBQ2MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92YXl1dmVjdG9yLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/NTY4NyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJjaGl2ZUJveEljb24gfSBmcm9tIFwiLi9BcmNoaXZlQm94SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIERvY3VtZW50VGV4dEljb24gfSBmcm9tIFwiLi9Eb2N1bWVudFRleHRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmVBbHRJY29uIH0gZnJvbSBcIi4vR2xvYmVBbHRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSG9tZUljb24gfSBmcm9tIFwiLi9Ib21lSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNoaWVsZENoZWNrSWNvbiB9IGZyb20gXCIuL1NoaWVsZENoZWNrSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRydWNrSWNvbiB9IGZyb20gXCIuL1RydWNrSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArchiveBoxIcon,DocumentTextIcon,GlobeAltIcon,HomeIcon,ShieldCheckIcon,TruckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ArrowRightIcon,CalculatorIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowRightIcon,CalculatorIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRightIcon: () => (/* reexport safe */ _ArrowRightIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CalculatorIcon: () => (/* reexport safe */ _CalculatorIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowRightIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowRightIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _CalculatorIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CalculatorIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CalculatorIcon.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1JpZ2h0SWNvbixDYWxjdWxhdG9ySWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDK0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92YXl1dmVjdG9yLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/ZmFjZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJyb3dSaWdodEljb24gfSBmcm9tIFwiLi9BcnJvd1JpZ2h0SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhbGN1bGF0b3JJY29uIH0gZnJvbSBcIi4vQ2FsY3VsYXRvckljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowRightIcon,CalculatorIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Bars3Icon,PhoneIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Bars3Icon,PhoneIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bars3Icon: () => (/* reexport safe */ _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   PhoneIcon: () => (/* reexport safe */ _PhoneIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   XMarkIcon: () => (/* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Bars3Icon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _PhoneIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./PhoneIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJzM0ljb24sUGhvbmVJY29uLFhNYXJrSWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUNxRDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmF5dXZlY3Rvci1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzP2ExY2UiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJhcnMzSWNvbiB9IGZyb20gXCIuL0JhcnMzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFBob25lSWNvbiB9IGZyb20gXCIuL1Bob25lSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFhNYXJrSWNvbiB9IGZyb20gXCIuL1hNYXJrSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Bars3Icon,PhoneIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ChevronDownIcon!=!./node_modules/@heroicons/react/20/solid/esm/index.js":
/*!*********************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronDownIcon!=!./node_modules/@heroicons/react/20/solid/esm/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ChevronDownIcon: () => (/* reexport safe */ _ChevronDownIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _ChevronDownIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ChevronDownIcon.js */ "./node_modules/@heroicons/react/20/solid/esm/ChevronDownIcon.js");



/***/ }),

/***/ "__barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,StarIcon!=!./node_modules/@heroicons/react/24/solid/esm/index.js":
/*!***********************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,StarIcon!=!./node_modules/@heroicons/react/24/solid/esm/index.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChevronLeftIcon: () => (/* reexport safe */ _ChevronLeftIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ChevronRightIcon: () => (/* reexport safe */ _ChevronRightIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   StarIcon: () => (/* reexport safe */ _StarIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ChevronLeftIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ChevronLeftIcon.js */ \"./node_modules/@heroicons/react/24/solid/esm/ChevronLeftIcon.js\");\n/* harmony import */ var _ChevronRightIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ChevronRightIcon.js */ \"./node_modules/@heroicons/react/24/solid/esm/ChevronRightIcon.js\");\n/* harmony import */ var _StarIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./StarIcon.js */ \"./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGV2cm9uTGVmdEljb24sQ2hldnJvblJpZ2h0SWNvbixTdGFySWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvc29saWQvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFDaUU7QUFDRSIsInNvdXJjZXMiOlsid2VicGFjazovL3ZheXV2ZWN0b3ItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9zb2xpZC9lc20vaW5kZXguanM/MjFmZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hldnJvbkxlZnRJY29uIH0gZnJvbSBcIi4vQ2hldnJvbkxlZnRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hldnJvblJpZ2h0SWNvbiB9IGZyb20gXCIuL0NoZXZyb25SaWdodEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTdGFySWNvbiB9IGZyb20gXCIuL1N0YXJJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,StarIcon!=!./node_modules/@heroicons/react/24/solid/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!***************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClockIcon: () => (/* reexport safe */ _ClockIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   EnvelopeIcon: () => (/* reexport safe */ _EnvelopeIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   MapPinIcon: () => (/* reexport safe */ _MapPinIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   PhoneIcon: () => (/* reexport safe */ _PhoneIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ClockIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ClockIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _EnvelopeIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EnvelopeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _MapPinIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MapPinIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _PhoneIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PhoneIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DbG9ja0ljb24sRW52ZWxvcGVJY29uLE1hcFBpbkljb24sUGhvbmVJY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDcUQ7QUFDTTtBQUNKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmF5dXZlY3Rvci1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzP2UyYTkiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENsb2NrSWNvbiB9IGZyb20gXCIuL0Nsb2NrSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEVudmVsb3BlSWNvbiB9IGZyb20gXCIuL0VudmVsb3BlSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1hcFBpbkljb24gfSBmcm9tIFwiLi9NYXBQaW5JY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGhvbmVJY29uIH0gZnJvbSBcIi4vUGhvbmVJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.tsx */ \"./pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const footerLinks = {\n        services: [\n            {\n                name: \"Residential Relocation\",\n                href: \"/services/residential\"\n            },\n            {\n                name: \"Vehicle Transportation\",\n                href: \"/services/vehicle\"\n            },\n            {\n                name: \"Documentation Support\",\n                href: \"/services/documentation\"\n            },\n            {\n                name: \"Storage Solutions\",\n                href: \"/services/storage\"\n            }\n        ],\n        company: [\n            {\n                name: \"About Us\",\n                href: \"/about\"\n            },\n            {\n                name: \"How It Works\",\n                href: \"/how-it-works\"\n            },\n            {\n                name: \"Careers\",\n                href: \"/careers\"\n            },\n            {\n                name: \"Press\",\n                href: \"/press\"\n            }\n        ],\n        support: [\n            {\n                name: \"Contact Us\",\n                href: \"/contact\"\n            },\n            {\n                name: \"FAQ\",\n                href: \"/faq\"\n            },\n            {\n                name: \"Track Shipment\",\n                href: \"/track\"\n            },\n            {\n                name: \"Customer Portal\",\n                href: \"/portal\"\n            }\n        ],\n        legal: [\n            {\n                name: \"Privacy Policy\",\n                href: \"/privacy\"\n            },\n            {\n                name: \"Terms of Service\",\n                href: \"/terms\"\n            },\n            {\n                name: \"Cookie Policy\",\n                href: \"/cookies\"\n            },\n            {\n                name: \"Insurance\",\n                href: \"/insurance\"\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            name: \"Facebook\",\n            href: \"https://facebook.com/vayuvector\",\n            icon: \"facebook\"\n        },\n        {\n            name: \"Twitter\",\n            href: \"https://twitter.com/vayuvector\",\n            icon: \"twitter\"\n        },\n        {\n            name: \"LinkedIn\",\n            href: \"https://linkedin.com/company/vayuvector\",\n            icon: \"linkedin\"\n        },\n        {\n            name: \"Instagram\",\n            href: \"https://instagram.com/vayuvector\",\n            icon: \"instagram\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom py-12 lg:py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center space-x-2 mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            src: \"/logo-dark.svg\",\n                                            alt: \"VayuVector Logo\",\n                                            width: 160,\n                                            height: 48,\n                                            className: \"h-8 w-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-6 max-w-sm\",\n                                        children: \"Your trusted global relocation partner. Moving lives, not just belongings, with comprehensive door-to-door services worldwide.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.PhoneIcon, {\n                                                        className: \"h-5 w-5 text-accent-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"tel:******-VAYU-VEC\",\n                                                        className: \"text-gray-300 hover:text-white transition-colors\",\n                                                        children: \"******-VAYU-VEC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.EnvelopeIcon, {\n                                                        className: \"h-5 w-5 text-accent-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"mailto:<EMAIL>\",\n                                                        className: \"text-gray-300 hover:text-white transition-colors\",\n                                                        children: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.MapPinIcon, {\n                                                        className: \"h-5 w-5 text-accent-500 mt-0.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: [\n                                                            \"123 Logistics Ave\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 86,\n                                                                columnNumber: 36\n                                                            }, undefined),\n                                                            \"Global City, GC 12345\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ClockIcon, {\n                                                        className: \"h-5 w-5 text-accent-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"24/7 Customer Support\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Services\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.services.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.company.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Support\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.support.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Legal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.legal.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 pt-8 border-t border-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: \"Stay Updated\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-4\",\n                                    children: \"Get relocation tips and updates delivered to your inbox.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Enter your email\",\n                                            className: \"flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"btn-primary whitespace-nowrap\",\n                                            children: \"Subscribe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" VayuVector. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: social.href,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"text-gray-400 hover:text-white transition-colors duration-200\",\n                                        \"aria-label\": social.name,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: social.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-5 h-5 bg-gray-600 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, social.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Footer.tsx\n");

/***/ }),

/***/ "./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,PhoneIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=Bars3Icon,PhoneIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_heroicons_react_20_solid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon!=!@heroicons/react/20/solid */ \"__barrel_optimize__?names=ChevronDownIcon!=!./node_modules/@heroicons/react/20/solid/esm/index.js\");\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Home\",\n        href: \"/\"\n    },\n    {\n        name: \"Services\",\n        href: \"/services\",\n        children: [\n            {\n                name: \"Residential Relocation\",\n                href: \"/services/residential\"\n            },\n            {\n                name: \"Vehicle Transportation\",\n                href: \"/services/vehicle\"\n            },\n            {\n                name: \"Documentation Support\",\n                href: \"/services/documentation\"\n            }\n        ]\n    },\n    {\n        name: \"How It Works\",\n        href: \"/how-it-works\"\n    },\n    {\n        name: \"About\",\n        href: \"/about\"\n    },\n    {\n        name: \"Contact\",\n        href: \"/contact\"\n    }\n];\nconst Header = ()=>{\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [servicesOpen, setServicesOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const isScrolled = window.scrollY > 10;\n            setScrolled(isScrolled);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled ? \"bg-white/95 backdrop-blur-md shadow-soft\" : \"bg-transparent\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    src: \"/logo.svg\",\n                                    alt: \"VayuVector Logo\",\n                                    width: 200,\n                                    height: 60,\n                                    className: \"h-12 lg:h-14 w-auto\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: item.children ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: ()=>setServicesOpen(true),\n                                        onMouseLeave: ()=>setServicesOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `flex items-center space-x-1 px-3 py-2 text-sm font-medium transition-colors duration-200 ${isActive(item.href) ? \"text-secondary-600\" : scrolled ? \"text-gray-700 hover:text-secondary-600\" : \"text-white hover:text-secondary-200\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_heroicons_react_20_solid__WEBPACK_IMPORTED_MODULE_5__.ChevronDownIcon, {\n                                                        className: `h-4 w-4 transition-transform duration-200 ${servicesOpen ? \"rotate-180\" : \"\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            servicesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-medium border border-gray-100 py-2\",\n                                                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: child.href,\n                                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-secondary-600 transition-colors duration-200\",\n                                                        children: child.name\n                                                    }, child.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 27\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: `px-3 py-2 text-sm font-medium transition-colors duration-200 ${isActive(item.href) ? \"text-secondary-600\" : scrolled ? \"text-gray-700 hover:text-secondary-600\" : \"text-white hover:text-secondary-200\"}`,\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"tel:******-VAYU-VEC\",\n                                    className: `flex items-center space-x-2 px-3 py-2 text-sm font-medium transition-colors duration-200 ${scrolled ? \"text-gray-700 hover:text-secondary-600\" : \"text-white hover:text-secondary-200\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PhoneIcon, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"******-VAYU-VEC\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/quote\",\n                                    className: \"btn-secondary text-black font-semibold\",\n                                    children: \"Get Quote\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: `p-2 rounded-md transition-colors duration-200 ${scrolled ? \"text-gray-700 hover:text-secondary-600\" : \"text-white hover:text-secondary-200\"}`,\n                                onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open main menu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.XMarkIcon, {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.Bars3Icon, {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined),\n                mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 bg-white rounded-lg shadow-medium mt-2\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: `block px-3 py-2 text-base font-medium rounded-md transition-colors duration-200 ${isActive(item.href) ? \"text-secondary-600 bg-secondary-50\" : \"text-gray-700 hover:text-secondary-600 hover:bg-gray-50\"}`,\n                                            onClick: ()=>setMobileMenuOpen(false),\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 space-y-1\",\n                                            children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: child.href,\n                                                    className: \"block px-3 py-2 text-sm text-gray-600 hover:text-secondary-600 hover:bg-gray-50 rounded-md transition-colors duration-200\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: child.name\n                                                }, child.name, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 border-t border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"tel:******-VAYU-VEC\",\n                                        className: \"flex items-center space-x-2 px-3 py-2 text-base font-medium text-gray-700 hover:text-secondary-600 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PhoneIcon, {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"******-VAYU-VEC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/quote\",\n                                        className: \"block w-full mt-2 btn-secondary text-center text-black font-semibold\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Get Quote\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Header.tsx\n");

/***/ }),

/***/ "./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Header */ \"./components/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Footer */ \"./components/Footer.tsx\");\n\n\n\n\n\nconst Layout = ({ children, title = \"VayuVector - Your Global Relocation Partner\", description = \"Professional international relocation services. Moving lives, not just belongings. Door-to-door service with comprehensive support.\", keywords = \"international moving, relocation services, global shipping, door-to-door moving, expat services, international logistics\", ogImage = \"/images/og-image.jpg\", noIndex = false })=>{\n    const fullTitle = title.includes(\"VayuVector\") ? title : `${title} | VayuVector`;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: fullTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: keywords\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: fullTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: ogImage\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:site_name\",\n                        content: \"VayuVector\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:title\",\n                        content: fullTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:image\",\n                        content: ogImage\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"canonical\",\n                        href: `https://vayuvector.com${ false ? 0 : \"\"}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined),\n                    noIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex, nofollow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"Organization\",\n                                \"name\": \"VayuVector\",\n                                \"description\": description,\n                                \"url\": \"https://vayuvector.com\",\n                                \"logo\": \"https://vayuvector.com/logo.svg\",\n                                \"contactPoint\": {\n                                    \"@type\": \"ContactPoint\",\n                                    \"telephone\": \"******-VAYU-VEC\",\n                                    \"contactType\": \"customer service\",\n                                    \"availableLanguage\": [\n                                        \"English\"\n                                    ]\n                                },\n                                \"sameAs\": [\n                                    \"https://facebook.com/vayuvector\",\n                                    \"https://twitter.com/vayuvector\",\n                                    \"https://linkedin.com/company/vayuvector\"\n                                ],\n                                \"address\": {\n                                    \"@type\": \"PostalAddress\",\n                                    \"streetAddress\": \"123 Logistics Ave\",\n                                    \"addressLocality\": \"Global City\",\n                                    \"addressRegion\": \"GC\",\n                                    \"postalCode\": \"12345\",\n                                    \"addressCountry\": \"US\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-grow\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./components/Layout.tsx\n");

/***/ }),

/***/ "./components/QuoteCalculator.tsx":
/*!****************************************!*\
  !*** ./components/QuoteCalculator.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalculatorIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalculatorIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightIcon,CalculatorIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_3__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst QuoteCalculator = ()=>{\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        origin: {\n            country: \"\",\n            city: \"\"\n        },\n        destination: {\n            country: \"\",\n            city: \"\"\n        },\n        homeSize: \"\",\n        moveDate: \"\",\n        packingService: false,\n        storageNeeded: false,\n        storageDuration: 0\n    });\n    const handleInputChange = (field, value)=>{\n        if (field.includes(\".\")) {\n            const [parent, child] = field.split(\".\");\n            setFormData((prev)=>({\n                    ...prev,\n                    [parent]: {\n                        ...prev[parent] || {},\n                        [child]: value\n                    }\n                }));\n        } else {\n            setFormData((prev)=>({\n                    ...prev,\n                    [field]: value\n                }));\n        }\n    };\n    const calculateQuote = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/quotes/calculate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    origin: formData.origin,\n                    destination: formData.destination,\n                    homeSize: formData.homeSize,\n                    packingService: formData.packingService,\n                    storageNeeded: formData.storageNeeded,\n                    storageDuration: formData.storageDuration\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to calculate quote\");\n            }\n            const data = await response.json();\n            setResult(data.data);\n            setStep(4);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Quote calculated successfully!\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Failed to calculate quote. Please try again.\");\n            console.error(\"Quote calculation error:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const nextStep = ()=>{\n        if (step < 3) {\n            setStep(step + 1);\n        } else {\n            calculateQuote();\n        }\n    };\n    const prevStep = ()=>{\n        if (step > 1) {\n            setStep(step - 1);\n        }\n    };\n    const resetCalculator = ()=>{\n        setStep(1);\n        setResult(null);\n        setFormData({\n            origin: {\n                country: \"\",\n                city: \"\"\n            },\n            destination: {\n                country: \"\",\n                city: \"\"\n            },\n            homeSize: \"\",\n            moveDate: \"\",\n            packingService: false,\n            storageNeeded: false,\n            storageDuration: 0\n        });\n    };\n    const isStepValid = ()=>{\n        switch(step){\n            case 1:\n                return formData.origin.country && formData.origin.city && formData.destination.country && formData.destination.city;\n            case 2:\n                return formData.homeSize && formData.moveDate;\n            case 3:\n                return true; // Optional services\n            default:\n                return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card p-8 max-w-2xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: [\n                                    \"Step \",\n                                    step,\n                                    \" of \",\n                                    result ? 4 : 3\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    step === 1 && \"Locations\",\n                                    step === 2 && \"Move Details\",\n                                    step === 3 && \"Additional Services\",\n                                    step === 4 && \"Your Quote\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: `${step / (result ? 4 : 3) * 100}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    x: 20\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0\n                },\n                exit: {\n                    opacity: 0,\n                    x: -20\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: [\n                    step === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Where are you moving?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Moving From\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Country\",\n                                                value: formData.origin.country,\n                                                onChange: (e)=>handleInputChange(\"origin.country\", e.target.value),\n                                                className: \"input mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"City\",\n                                                value: formData.origin.city,\n                                                onChange: (e)=>handleInputChange(\"origin.city\", e.target.value),\n                                                className: \"input\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Moving To\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Country\",\n                                                value: formData.destination.country,\n                                                onChange: (e)=>handleInputChange(\"destination.country\", e.target.value),\n                                                className: \"input mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"City\",\n                                                value: formData.destination.city,\n                                                onChange: (e)=>handleInputChange(\"destination.city\", e.target.value),\n                                                className: \"input\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, undefined),\n                    step === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Tell us about your move\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Home Size\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: formData.homeSize,\n                                        onChange: (e)=>handleInputChange(\"homeSize\", e.target.value),\n                                        className: \"input\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select home size\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"studio\",\n                                                children: \"Studio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"1-bedroom\",\n                                                children: \"1 Bedroom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"2-bedroom\",\n                                                children: \"2 Bedroom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"3-bedroom\",\n                                                children: \"3 Bedroom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"4-bedroom\",\n                                                children: \"4 Bedroom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"5-bedroom\",\n                                                children: \"5+ Bedroom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"office\",\n                                                children: \"Office\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Preferred Move Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        value: formData.moveDate,\n                                        onChange: (e)=>handleInputChange(\"moveDate\", e.target.value),\n                                        className: \"input\",\n                                        min: new Date().toISOString().split(\"T\")[0]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, undefined),\n                    step === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Additional Services\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: formData.packingService,\n                                                onChange: (e)=>handleInputChange(\"packingService\", e.target.checked),\n                                                className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-gray-700\",\n                                                children: \"Professional packing service (+30% of base cost)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: formData.storageNeeded,\n                                                onChange: (e)=>handleInputChange(\"storageNeeded\", e.target.checked),\n                                                className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-gray-700\",\n                                                children: \"Storage service needed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    formData.storageNeeded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Storage Duration (months)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"1\",\n                                                max: \"24\",\n                                                value: formData.storageDuration,\n                                                onChange: (e)=>handleInputChange(\"storageDuration\", parseInt(e.target.value) || 0),\n                                                className: \"input w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, undefined),\n                    step === 4 && result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalculatorIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CalculatorIcon, {\n                                        className: \"h-12 w-12 text-primary-600 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Your Estimated Quote\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            \"From \",\n                                            formData.origin.city,\n                                            \", \",\n                                            formData.origin.country,\n                                            \" to\",\n                                            \" \",\n                                            formData.destination.city,\n                                            \", \",\n                                            formData.destination.country\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Base moving cost:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: [\n                                                        \"$\",\n                                                        result.basePrice.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        result.packingPrice > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Packing service:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: [\n                                                        \"$\",\n                                                        result.packingPrice.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        result.storagePrice > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Storage service:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: [\n                                                        \"$\",\n                                                        result.storagePrice.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                            className: \"border-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between text-lg font-bold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Total Estimated Cost:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-600\",\n                                                    children: [\n                                                        \"$\",\n                                                        result.totalPrice.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"This is an estimate. Final pricing may vary based on actual inventory and services.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-primary\",\n                                                children: \"Get Detailed Quote\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: resetCalculator,\n                                                className: \"btn-outline\",\n                                                children: \"Calculate Again\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, step, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined),\n            step < 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: prevStep,\n                        disabled: step === 1,\n                        className: \"btn-ghost disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: \"Previous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: nextStep,\n                        disabled: !isStepValid() || loading,\n                        className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: step === 3 ? \"Calculate Quote\" : \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalculatorIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ArrowRightIcon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuoteCalculator);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/QuoteCalculator.tsx\n");

/***/ }),

/***/ "./components/ServicesCarousel.tsx":
/*!*****************************************!*\
  !*** ./components/ServicesCarousel.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_ArchiveBoxIcon_DocumentTextIcon_GlobeAltIcon_HomeIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArchiveBoxIcon,DocumentTextIcon,GlobeAltIcon,HomeIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArchiveBoxIcon,DocumentTextIcon,GlobeAltIcon,HomeIcon,ShieldCheckIcon,TruckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst services = [\n    {\n        id: \"residential\",\n        title: \"Residential Relocation\",\n        description: \"Complete household moving services with professional packing and careful handling of your belongings.\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_DocumentTextIcon_GlobeAltIcon_HomeIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.HomeIcon,\n        features: [\n            \"Professional packing & unpacking\",\n            \"Furniture disassembly & assembly\",\n            \"Appliance disconnection & reconnection\",\n            \"Fragile item special handling\",\n            \"Room-by-room organization\"\n        ],\n        color: \"text-primary-600\",\n        bgColor: \"bg-primary-100\"\n    },\n    {\n        id: \"vehicle\",\n        title: \"Vehicle Transportation\",\n        description: \"Safe and secure transportation of your vehicles with full insurance coverage and tracking.\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_DocumentTextIcon_GlobeAltIcon_HomeIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.TruckIcon,\n        features: [\n            \"Door-to-door car shipping\",\n            \"Motorcycle transportation\",\n            \"Classic & luxury vehicle handling\",\n            \"International documentation\",\n            \"Real-time tracking\"\n        ],\n        color: \"text-secondary-600\",\n        bgColor: \"bg-secondary-100\"\n    },\n    {\n        id: \"documentation\",\n        title: \"Documentation Support\",\n        description: \"Complete assistance with all paperwork, visas, and legal requirements for your move.\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_DocumentTextIcon_GlobeAltIcon_HomeIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.DocumentTextIcon,\n        features: [\n            \"Visa & work permit guidance\",\n            \"Customs clearance assistance\",\n            \"School enrollment support\",\n            \"Banking setup coordination\",\n            \"Local registration help\"\n        ],\n        color: \"text-accent-600\",\n        bgColor: \"bg-accent-100\"\n    },\n    {\n        id: \"storage\",\n        title: \"Storage Solutions\",\n        description: \"Secure short-term and long-term storage options for your belongings during transition.\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_DocumentTextIcon_GlobeAltIcon_HomeIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ArchiveBoxIcon,\n        features: [\n            \"Climate-controlled facilities\",\n            \"Short & long-term options\",\n            \"Inventory management\",\n            \"24/7 security monitoring\",\n            \"Easy access scheduling\"\n        ],\n        color: \"text-purple-600\",\n        bgColor: \"bg-purple-100\"\n    },\n    {\n        id: \"global\",\n        title: \"Global Network\",\n        description: \"Worldwide coverage with local expertise in over 120 countries.\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_DocumentTextIcon_GlobeAltIcon_HomeIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.GlobeAltIcon,\n        features: [\n            \"120+ countries served\",\n            \"Local partner network\",\n            \"Cultural expertise\",\n            \"Multi-language support\",\n            \"Time zone coordination\"\n        ],\n        color: \"text-green-600\",\n        bgColor: \"bg-green-100\"\n    },\n    {\n        id: \"insurance\",\n        title: \"Insurance & Protection\",\n        description: \"Comprehensive insurance coverage and protection for your valuable belongings.\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_DocumentTextIcon_GlobeAltIcon_HomeIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ShieldCheckIcon,\n        features: [\n            \"Full replacement value coverage\",\n            \"Transit insurance included\",\n            \"Damage claim processing\",\n            \"Third-party liability\",\n            \"Peace of mind guarantee\"\n        ],\n        color: \"text-indigo-600\",\n        bgColor: \"bg-indigo-100\"\n    }\n];\nconst ServicesCarousel = ()=>{\n    const [activeService, setActiveService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Comprehensive Relocation Services\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                            children: \"From residential moves to vehicle transportation, we provide end-to-end solutions for your international relocation needs.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: services.map((service, index)=>{\n                                const IconComponent = service.icon;\n                                const isActive = activeService === index;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                    onClick: ()=>setActiveService(index),\n                                    className: `w-full text-left p-6 rounded-xl transition-all duration-300 ${isActive ? \"bg-white shadow-medium border-l-4 border-primary-600\" : \"bg-gray-50 hover:bg-white hover:shadow-soft\"}`,\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex-shrink-0 w-12 h-12 rounded-lg ${service.bgColor} flex items-center justify-center`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                    className: `h-6 w-6 ${service.color}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: `text-lg font-semibold mb-2 ${isActive ? \"text-primary-600\" : \"text-gray-900\"}`,\n                                                        children: service.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: service.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, service.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:pl-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5\n                                },\n                                className: \"card p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-16 h-16 rounded-xl ${services[activeService].bgColor} flex items-center justify-center`,\n                                                children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(services[activeService].icon, {\n                                                    className: `h-8 w-8 ${services[activeService].color}`\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: services[activeService].title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: services[activeService].description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold text-gray-900 mb-4\",\n                                                children: \"What's Included:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            services[activeService].features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.3,\n                                                        delay: index * 0.1\n                                                    },\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-primary-600 rounded-full flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-700\",\n                                                            children: feature\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 pt-6 border-t border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-primary w-full sm:w-auto\",\n                                            children: [\n                                                \"Learn More About \",\n                                                services[activeService].title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, activeService, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-20 grid grid-cols-2 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl md:text-3xl font-bold text-primary-600 mb-2\",\n                                    children: \"50K+\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: \"Successful Moves\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl md:text-3xl font-bold text-secondary-600 mb-2\",\n                                    children: \"120+\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: \"Countries Served\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl md:text-3xl font-bold text-accent-600 mb-2\",\n                                    children: \"15+\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: \"Years Experience\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl md:text-3xl font-bold text-green-600 mb-2\",\n                                    children: \"24/7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: \"Customer Support\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ServicesCarousel);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ServicesCarousel.tsx\n");

/***/ }),

/***/ "./components/Testimonials.tsx":
/*!*************************************!*\
  !*** ./components/Testimonials.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,StarIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,StarIcon!=!./node_modules/@heroicons/react/24/solid/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst testimonials = [\n    {\n        id: 1,\n        name: \"Sarah Johnson\",\n        role: \"Software Engineer\",\n        company: \"Tech Corp\",\n        location: \"London, UK\",\n        rating: 5,\n        content: \"VayuVector made our international move from San Francisco to London absolutely seamless. Their team handled everything from packing our delicate electronics to navigating customs. The communication was excellent throughout the entire process.\",\n        avatar: \"/images/testimonials/sarah.jpg\",\n        moveRoute: \"San Francisco → London\"\n    },\n    {\n        id: 2,\n        name: \"Michael Chen\",\n        role: \"Marketing Director\",\n        company: \"Global Solutions\",\n        location: \"Singapore\",\n        rating: 5,\n        content: \"Outstanding service! They moved our entire household from New York to Singapore, including our car. Everything arrived in perfect condition and on time. The customer portal made tracking our shipment so easy.\",\n        avatar: \"/images/testimonials/michael.jpg\",\n        moveRoute: \"New York → Singapore\"\n    },\n    {\n        id: 3,\n        name: \"Emma Rodriguez\",\n        role: \"Research Scientist\",\n        company: \"BioTech Labs\",\n        location: \"Barcelona, Spain\",\n        rating: 5,\n        content: \"The documentation support was incredible. VayuVector helped us with all the visa paperwork and customs requirements. They truly understand the challenges of international relocation.\",\n        avatar: \"/images/testimonials/emma.jpg\",\n        moveRoute: \"Boston → Barcelona\"\n    },\n    {\n        id: 4,\n        name: \"David Thompson\",\n        role: \"Finance Manager\",\n        company: \"Investment Bank\",\n        location: \"Tokyo, Japan\",\n        rating: 5,\n        content: \"Professional, reliable, and stress-free. The team went above and beyond to ensure our move to Tokyo was smooth. They even helped us find temporary accommodation while we searched for a permanent home.\",\n        avatar: \"/images/testimonials/david.jpg\",\n        moveRoute: \"Chicago → Tokyo\"\n    },\n    {\n        id: 5,\n        name: \"Lisa Anderson\",\n        role: \"Product Manager\",\n        company: \"StartupCo\",\n        location: \"Sydney, Australia\",\n        rating: 5,\n        content: \"VayuVector exceeded all our expectations. The packing was meticulous, the shipping was fast, and the unpacking service saved us weeks of work. Highly recommend for any international move!\",\n        avatar: \"/images/testimonials/lisa.jpg\",\n        moveRoute: \"Seattle → Sydney\"\n    }\n];\nconst Testimonials = ()=>{\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutoPlaying, setIsAutoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Auto-advance testimonials\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAutoPlaying) return;\n        const interval = setInterval(()=>{\n            setCurrentIndex((prevIndex)=>prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1);\n        }, 5000);\n        return ()=>clearInterval(interval);\n    }, [\n        isAutoPlaying\n    ]);\n    const goToNext = ()=>{\n        setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1);\n    };\n    const goToPrevious = ()=>{\n        setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1);\n    };\n    const goToSlide = (index)=>{\n        setCurrentIndex(index);\n    };\n    const currentTestimonial = testimonials[currentIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"What Our Customers Say\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Don't just take our word for it. Here's what professionals around the world say about their VayuVector experience.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            onMouseEnter: ()=>setIsAutoPlaying(false),\n                            onMouseLeave: ()=>setIsAutoPlaying(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            y: -20\n                                        },\n                                        transition: {\n                                            duration: 0.5\n                                        },\n                                        className: \"card p-8 md:p-12 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center mb-6\",\n                                                children: [\n                                                    ...Array(currentTestimonial.rating)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.StarIcon, {\n                                                        className: \"h-6 w-6 text-accent-500\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                className: \"text-lg md:text-xl text-gray-700 mb-8 leading-relaxed\",\n                                                children: [\n                                                    '\"',\n                                                    currentTestimonial.content,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-gradient-to-br from-primary-400 to-secondary-600 rounded-full flex items-center justify-center text-white font-bold text-xl\",\n                                                            children: currentTestimonial.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center md:text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-semibold text-gray-900 text-lg\",\n                                                                children: currentTestimonial.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-600\",\n                                                                children: [\n                                                                    currentTestimonial.role,\n                                                                    \" at \",\n                                                                    currentTestimonial.company\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                children: currentTestimonial.moveRoute\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, currentIndex, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: goToPrevious,\n                                    className: \"absolute left-4 top-1/2 transform -translate-y-1/2 p-2 rounded-full bg-white shadow-medium hover:shadow-hard transition-all duration-200 text-gray-600 hover:text-primary-600\",\n                                    \"aria-label\": \"Previous testimonial\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ChevronLeftIcon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: goToNext,\n                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 p-2 rounded-full bg-white shadow-medium hover:shadow-hard transition-all duration-200 text-gray-600 hover:text-primary-600\",\n                                    \"aria-label\": \"Next testimonial\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ChevronRightIcon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mt-8 space-x-2\",\n                            children: testimonials.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>goToSlide(index),\n                                    className: `w-3 h-3 rounded-full transition-all duration-200 ${index === currentIndex ? \"bg-primary-600 scale-110\" : \"bg-gray-300 hover:bg-gray-400\"}`,\n                                    \"aria-label\": `Go to testimonial ${index + 1}`\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mt-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-primary-600 mb-2\",\n                                            children: \"4.9/5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Average Rating\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center mt-2\",\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.StarIcon, {\n                                                    className: \"h-4 w-4 text-accent-500\"\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-primary-600 mb-2\",\n                                            children: \"2,500+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Happy Customers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-primary-600 mb-2\",\n                                            children: \"98%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Would Recommend\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Testimonials);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Testimonials.tsx\n");

/***/ }),

/***/ "./components/WorldMap.tsx":
/*!*********************************!*\
  !*** ./components/WorldMap.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst mapPins = [\n    // North America\n    {\n        id: \"nyc\",\n        name: \"New York\",\n        country: \"USA\",\n        x: 25,\n        y: 35,\n        moves: 1250,\n        popular: true\n    },\n    {\n        id: \"sf\",\n        name: \"San Francisco\",\n        country: \"USA\",\n        x: 15,\n        y: 38,\n        moves: 980,\n        popular: true\n    },\n    {\n        id: \"toronto\",\n        name: \"Toronto\",\n        country: \"Canada\",\n        x: 23,\n        y: 32,\n        moves: 650,\n        popular: false\n    },\n    // Europe\n    {\n        id: \"london\",\n        name: \"London\",\n        country: \"UK\",\n        x: 50,\n        y: 30,\n        moves: 1450,\n        popular: true\n    },\n    {\n        id: \"paris\",\n        name: \"Paris\",\n        country: \"France\",\n        x: 52,\n        y: 32,\n        moves: 890,\n        popular: false\n    },\n    {\n        id: \"berlin\",\n        name: \"Berlin\",\n        country: \"Germany\",\n        x: 55,\n        y: 30,\n        moves: 720,\n        popular: false\n    },\n    {\n        id: \"zurich\",\n        name: \"Zurich\",\n        country: \"Switzerland\",\n        x: 54,\n        y: 33,\n        moves: 560,\n        popular: false\n    },\n    // Asia\n    {\n        id: \"tokyo\",\n        name: \"Tokyo\",\n        country: \"Japan\",\n        x: 85,\n        y: 38,\n        moves: 1100,\n        popular: true\n    },\n    {\n        id: \"singapore\",\n        name: \"Singapore\",\n        country: \"Singapore\",\n        x: 78,\n        y: 55,\n        moves: 950,\n        popular: true\n    },\n    {\n        id: \"hongkong\",\n        name: \"Hong Kong\",\n        country: \"China\",\n        x: 82,\n        y: 45,\n        moves: 840,\n        popular: false\n    },\n    {\n        id: \"dubai\",\n        name: \"Dubai\",\n        country: \"UAE\",\n        x: 65,\n        y: 45,\n        moves: 670,\n        popular: false\n    },\n    // Australia\n    {\n        id: \"sydney\",\n        name: \"Sydney\",\n        country: \"Australia\",\n        x: 88,\n        y: 75,\n        moves: 780,\n        popular: true\n    },\n    {\n        id: \"melbourne\",\n        name: \"Melbourne\",\n        country: \"Australia\",\n        x: 86,\n        y: 78,\n        moves: 520,\n        popular: false\n    }\n];\nconst WorldMap = ()=>{\n    const [hoveredPin, setHoveredPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPin, setSelectedPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handlePinClick = (pinId)=>{\n        setSelectedPin(selectedPin === pinId ? null : pinId);\n    };\n    const getSelectedPinData = ()=>{\n        return mapPins.find((pin)=>pin.id === selectedPin);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section bg-gradient-to-br from-primary-50 to-secondary-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Global Coverage, Local Expertise\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                            children: \"With our worldwide network of partners and agents, we provide seamless relocation services to over 120 countries. Click on the pins to explore our most popular destinations.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-full h-96 md:h-[500px] bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl overflow-hidden shadow-soft\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                viewBox: \"0 0 1000 500\",\n                                                className: \"w-full h-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M150 200 L300 180 L350 220 L320 280 L200 300 L150 250 Z\",\n                                                        fill: \"#1E3A8A\",\n                                                        opacity: \"0.3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M400 150 L600 140 L650 200 L620 250 L500 260 L450 200 Z\",\n                                                        fill: \"#1E3A8A\",\n                                                        opacity: \"0.3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M700 200 L850 190 L900 240 L880 300 L750 310 L700 250 Z\",\n                                                        fill: \"#1E3A8A\",\n                                                        opacity: \"0.3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M800 350 L900 340 L920 380 L900 420 L820 430 L800 390 Z\",\n                                                        fill: \"#1E3A8A\",\n                                                        opacity: \"0.3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        mapPins.map((pin)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                className: \"absolute cursor-pointer\",\n                                                style: {\n                                                    left: `${pin.x}%`,\n                                                    top: `${pin.y}%`,\n                                                    transform: \"translate(-50%, -50%)\"\n                                                },\n                                                onMouseEnter: ()=>setHoveredPin(pin.id),\n                                                onMouseLeave: ()=>setHoveredPin(null),\n                                                onClick: ()=>handlePinClick(pin.id),\n                                                whileHover: {\n                                                    scale: 1.2\n                                                },\n                                                whileTap: {\n                                                    scale: 0.9\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `w-4 h-4 rounded-full border-2 border-white shadow-medium transition-all duration-200 ${pin.popular ? \"bg-accent-500\" : \"bg-primary-600\"} ${hoveredPin === pin.id || selectedPin === pin.id ? \"scale-150 shadow-glow\" : \"\"}`,\n                                                        children: pin.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 rounded-full bg-accent-500 animate-ping opacity-75\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    hoveredPin === pin.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 10\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg whitespace-nowrap shadow-hard\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: pin.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-300\",\n                                                                children: [\n                                                                    pin.moves,\n                                                                    \" moves completed\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, pin.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"absolute inset-0 w-full h-full pointer-events-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                        id: \"connectionGradient\",\n                                                        x1: \"0%\",\n                                                        y1: \"0%\",\n                                                        x2: \"100%\",\n                                                        y2: \"0%\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"0%\",\n                                                                stopColor: \"#0891B2\",\n                                                                stopOpacity: \"0.3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"50%\",\n                                                                stopColor: \"#F97316\",\n                                                                stopOpacity: \"0.6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"100%\",\n                                                                stopColor: \"#1E3A8A\",\n                                                                stopOpacity: \"0.3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: `M ${mapPins[0].x * 10} ${mapPins[0].y * 5} Q 400 150 ${mapPins[3].x * 10} ${mapPins[3].y * 5}`,\n                                                    stroke: \"url(#connectionGradient)\",\n                                                    strokeWidth: \"2\",\n                                                    fill: \"none\",\n                                                    className: \"animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: `M ${mapPins[3].x * 10} ${mapPins[3].y * 5} Q 650 300 ${mapPins[7].x * 10} ${mapPins[7].y * 5}`,\n                                                    stroke: \"url(#connectionGradient)\",\n                                                    strokeWidth: \"2\",\n                                                    fill: \"none\",\n                                                    className: \"animate-pulse\",\n                                                    style: {\n                                                        animationDelay: \"1s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                selectedPin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"mt-8 card p-6\",\n                                    children: (()=>{\n                                        const pinData = getSelectedPinData();\n                                        if (!pinData) return null;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-gray-900\",\n                                                            children: [\n                                                                pinData.name,\n                                                                \", \",\n                                                                pinData.country\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mt-1\",\n                                                            children: [\n                                                                pinData.moves,\n                                                                \" successful relocations completed\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        pinData.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-block mt-2 px-3 py-1 bg-accent-100 text-accent-700 text-sm font-medium rounded-full\",\n                                                            children: \"Popular Destination\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"btn-primary\",\n                                                    children: [\n                                                        \"Get Quote to \",\n                                                        pinData.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 21\n                                        }, undefined);\n                                    })()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-8 mt-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-primary-600 mb-2\",\n                                            children: \"120+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Countries Served\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-secondary-600 mb-2\",\n                                            children: \"500+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Partner Agents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-accent-600 mb-2\",\n                                            children: \"50K+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Moves Completed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-green-600 mb-2\",\n                                            children: \"99.2%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Success Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WorldMap);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/WorldMap.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_query_devtools__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query/devtools */ \"react-query/devtools\");\n/* harmony import */ var react_query_devtools__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_query_devtools__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_3__]);\nreact_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(()=>new react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    retry: 1,\n                    refetchOnWindowFocus: false,\n                    staleTime: 5 * 60 * 1000\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_app.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: \"#363636\",\n                        color: \"#fff\"\n                    },\n                    success: {\n                        duration: 3000,\n                        iconTheme: {\n                            primary: \"#10B981\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        duration: 5000,\n                        iconTheme: {\n                            primary: \"#EF4444\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_app.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query_devtools__WEBPACK_IMPORTED_MODULE_2__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_app.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_app.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preload\",\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap\",\n                        as: \"style\",\n                        onLoad: (e)=>{\n                            const target = e.target;\n                            target.onload = null;\n                            target.rel = \"stylesheet\";\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noscript\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            href: \"https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap\",\n                            rel: \"stylesheet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://api.vayuvector.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://www.google-analytics.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://maps.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#1E3A8A\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#1E3A8A\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.tsx\");\n/* harmony import */ var _components_QuoteCalculator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/QuoteCalculator */ \"./components/QuoteCalculator.tsx\");\n/* harmony import */ var _components_ServicesCarousel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ServicesCarousel */ \"./components/ServicesCarousel.tsx\");\n/* harmony import */ var _components_Testimonials__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Testimonials */ \"./components/Testimonials.tsx\");\n/* harmony import */ var _components_WorldMap__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/WorldMap */ \"./components/WorldMap.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_QuoteCalculator__WEBPACK_IMPORTED_MODULE_4__, _components_ServicesCarousel__WEBPACK_IMPORTED_MODULE_5__, _components_Testimonials__WEBPACK_IMPORTED_MODULE_6__, _components_WorldMap__WEBPACK_IMPORTED_MODULE_7__]);\n([_components_QuoteCalculator__WEBPACK_IMPORTED_MODULE_4__, _components_ServicesCarousel__WEBPACK_IMPORTED_MODULE_5__, _components_Testimonials__WEBPACK_IMPORTED_MODULE_6__, _components_WorldMap__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst HomePage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"VayuVector - Your Global Relocation Partner\",\n        description: \"Professional international relocation services. Moving lives, not just belongings. Door-to-door service with comprehensive support for expats and professionals worldwide.\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-900 via-primary-800 to-secondary-600 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-hero-pattern\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/4 w-64 h-64 bg-secondary-500/20 rounded-full blur-3xl animate-float\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent-500/20 rounded-full blur-3xl animate-float\",\n                                style: {\n                                    animationDelay: \"1s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 container-custom text-center text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 animate-fade-in-up\",\n                                    children: [\n                                        \"Your Global\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-secondary-500\",\n                                            children: \"Relocation Partner\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl lg:text-3xl mb-8 text-gray-200 animate-fade-in-up\",\n                                    style: {\n                                        animationDelay: \"0.2s\"\n                                    },\n                                    children: \"Moving Lives, Not Just Belongings\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg md:text-xl mb-12 text-gray-300 max-w-3xl mx-auto animate-fade-in-up\",\n                                    style: {\n                                        animationDelay: \"0.4s\"\n                                    },\n                                    children: \"Professional door-to-door relocation services for international professionals. From packing to destination setup, we handle every detail of your global move.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center items-center animate-fade-in-up\",\n                                    style: {\n                                        animationDelay: \"0.6s\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/quote\",\n                                            className: \"btn-secondary text-lg px-8 py-4 shadow-glow-accent hover:shadow-glow-accent text-black font-semibold\",\n                                            children: \"Get Free Quote\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/how-it-works\",\n                                            className: \"btn-outline text-lg px-8 py-4 border-secondary-500 text-secondary-500 hover:bg-secondary-500 hover:text-black\",\n                                            children: \"How It Works\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 animate-fade-in-up\",\n                                    style: {\n                                        animationDelay: \"0.8s\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl md:text-4xl font-bold text-secondary-400 mb-2\",\n                                                    children: \"15+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Years Experience\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl md:text-4xl font-bold text-secondary-400 mb-2\",\n                                                    children: \"50K+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Successful Moves\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl md:text-4xl font-bold text-secondary-400 mb-2\",\n                                                    children: \"120+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Countries Served\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Get Your Moving Quote in Minutes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600\",\n                                    children: \"Tell us about your move and get an instant estimate\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuoteCalculator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ServicesCarousel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WorldMap__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Testimonials__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-query":
/*!******************************!*\
  !*** external "react-query" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-query");

/***/ }),

/***/ "react-query/devtools":
/*!***************************************!*\
  !*** external "react-query/devtools" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-query/devtools");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "framer-motion":
/*!********************************!*\
  !*** external "framer-motion" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("framer-motion");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();