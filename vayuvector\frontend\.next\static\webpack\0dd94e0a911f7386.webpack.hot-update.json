{"c": ["pages/_app", "webpack"], "r": ["pages/quote", "/_error"], "m": ["./components/DetailedQuoteForm.tsx", "./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js", "./node_modules/@heroicons/react/24/outline/esm/BookmarkIcon.js", "./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CMilind-New%5Caugument%5Cbase%5Cvayuvector%5Cfrontend%5Cpages%5Cquote.tsx&page=%2Fquote!", "./node_modules/react-hook-form/dist/index.esm.mjs", "./pages/quote.tsx", "__barrel_optimize__?names=ArrowLeftIcon,ArrowRightIcon,BookmarkIcon,CheckCircleIcon,EnvelopeIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CMilind-New%5Caugument%5Cbase%5Cvayuvector%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!"]}