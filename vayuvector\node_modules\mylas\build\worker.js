"use strict";var _chunk3EDNT3AMjs = require('./chunk-3EDNT3AM.js');var _chunkM3H36CX2js = require('./chunk-M3H36CX2.js');var _worker_threads = require('worker_threads');_worker_threads.parentPort.once("message",async({method:e,path:s,data:i})=>{try{if(e===0&&_worker_threads.parentPort.postMessage(await _chunkM3H36CX2js.e.load(s)),e===1&&await _chunkM3H36CX2js.e.save(s,i),e===2&&_worker_threads.parentPort.postMessage(await _chunkM3H36CX2js.f.load(s)),e===3&&_worker_threads.parentPort.postMessage(await _chunkM3H36CX2js.f.load(s,void 0,!0)),e===4&&await _chunkM3H36CX2js.f.save(s,i),e===6&&await _chunk3EDNT3AMjs.a.save(s,Buffer.from(i)),e===5){let o=await _chunk3EDNT3AMjs.a.load(s),n=new Uint8Array(new SharedArrayBuffer(o.byteLength));n.set(o),_worker_threads.parentPort.postMessage(n)}process.exit(0)}catch (e2){process.exit(1)}});
