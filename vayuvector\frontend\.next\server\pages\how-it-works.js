/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/how-it-works";
exports.ids = ["pages/how-it-works"];
exports.modules = {

/***/ "__barrel_optimize__?names=ArchiveBoxIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,DocumentMagnifyingGlassIcon,GlobeAltIcon,HomeIcon,TruckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArchiveBoxIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,DocumentMagnifyingGlassIcon,GlobeAltIcon,HomeIcon,TruckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArchiveBoxIcon: () => (/* reexport safe */ _ArchiveBoxIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ChatBubbleLeftRightIcon: () => (/* reexport safe */ _ChatBubbleLeftRightIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   CheckCircleIcon: () => (/* reexport safe */ _CheckCircleIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   ClockIcon: () => (/* reexport safe */ _ClockIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   DocumentMagnifyingGlassIcon: () => (/* reexport safe */ _DocumentMagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   GlobeAltIcon: () => (/* reexport safe */ _GlobeAltIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   HomeIcon: () => (/* reexport safe */ _HomeIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   TruckIcon: () => (/* reexport safe */ _TruckIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArchiveBoxIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArchiveBoxIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArchiveBoxIcon.js\");\n/* harmony import */ var _ChatBubbleLeftRightIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ChatBubbleLeftRightIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _CheckCircleIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CheckCircleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _ClockIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ClockIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _DocumentMagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DocumentMagnifyingGlassIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/DocumentMagnifyingGlassIcon.js\");\n/* harmony import */ var _GlobeAltIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./GlobeAltIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _HomeIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./HomeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _TruckIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TruckIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/TruckIcon.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcmNoaXZlQm94SWNvbixDaGF0QnViYmxlTGVmdFJpZ2h0SWNvbixDaGVja0NpcmNsZUljb24sQ2xvY2tJY29uLERvY3VtZW50TWFnbmlmeWluZ0dsYXNzSWNvbixHbG9iZUFsdEljb24sSG9tZUljb24sVHJ1Y2tJY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUMrRDtBQUNrQjtBQUNoQjtBQUNaO0FBQ29DO0FBQzlCO0FBQ1IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92YXl1dmVjdG9yLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/MDFjNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJjaGl2ZUJveEljb24gfSBmcm9tIFwiLi9BcmNoaXZlQm94SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoYXRCdWJibGVMZWZ0UmlnaHRJY29uIH0gZnJvbSBcIi4vQ2hhdEJ1YmJsZUxlZnRSaWdodEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVja0NpcmNsZUljb24gfSBmcm9tIFwiLi9DaGVja0NpcmNsZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDbG9ja0ljb24gfSBmcm9tIFwiLi9DbG9ja0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBEb2N1bWVudE1hZ25pZnlpbmdHbGFzc0ljb24gfSBmcm9tIFwiLi9Eb2N1bWVudE1hZ25pZnlpbmdHbGFzc0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iZUFsdEljb24gfSBmcm9tIFwiLi9HbG9iZUFsdEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIb21lSWNvbiB9IGZyb20gXCIuL0hvbWVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVHJ1Y2tJY29uIH0gZnJvbSBcIi4vVHJ1Y2tJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArchiveBoxIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,DocumentMagnifyingGlassIcon,GlobeAltIcon,HomeIcon,TruckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Bars3Icon,PhoneIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Bars3Icon,PhoneIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bars3Icon: () => (/* reexport safe */ _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   PhoneIcon: () => (/* reexport safe */ _PhoneIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   XMarkIcon: () => (/* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Bars3Icon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _PhoneIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./PhoneIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJzM0ljb24sUGhvbmVJY29uLFhNYXJrSWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUNxRDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmF5dXZlY3Rvci1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzP2ExY2UiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJhcnMzSWNvbiB9IGZyb20gXCIuL0JhcnMzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFBob25lSWNvbiB9IGZyb20gXCIuL1Bob25lSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFhNYXJrSWNvbiB9IGZyb20gXCIuL1hNYXJrSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Bars3Icon,PhoneIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ChevronDownIcon!=!./node_modules/@heroicons/react/20/solid/esm/index.js":
/*!*********************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronDownIcon!=!./node_modules/@heroicons/react/20/solid/esm/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ChevronDownIcon: () => (/* reexport safe */ _ChevronDownIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _ChevronDownIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ChevronDownIcon.js */ "./node_modules/@heroicons/react/20/solid/esm/ChevronDownIcon.js");



/***/ }),

/***/ "__barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!***************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClockIcon: () => (/* reexport safe */ _ClockIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   EnvelopeIcon: () => (/* reexport safe */ _EnvelopeIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   MapPinIcon: () => (/* reexport safe */ _MapPinIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   PhoneIcon: () => (/* reexport safe */ _PhoneIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ClockIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ClockIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _EnvelopeIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EnvelopeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _MapPinIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MapPinIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _PhoneIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PhoneIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DbG9ja0ljb24sRW52ZWxvcGVJY29uLE1hcFBpbkljb24sUGhvbmVJY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDcUQ7QUFDTTtBQUNKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmF5dXZlY3Rvci1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzP2UyYTkiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENsb2NrSWNvbiB9IGZyb20gXCIuL0Nsb2NrSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEVudmVsb3BlSWNvbiB9IGZyb20gXCIuL0VudmVsb3BlSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1hcFBpbkljb24gfSBmcm9tIFwiLi9NYXBQaW5JY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGhvbmVJY29uIH0gZnJvbSBcIi4vUGhvbmVJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fhow-it-works&preferredRegion=&absolutePagePath=.%2Fpages%5Chow-it-works.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fhow-it-works&preferredRegion=&absolutePagePath=.%2Fpages%5Chow-it-works.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\how-it-works.tsx */ \"./pages/how-it-works.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/how-it-works\",\n        pathname: \"/how-it-works\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_how_it_works_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fhow-it-works&preferredRegion=&absolutePagePath=.%2Fpages%5Chow-it-works.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const footerLinks = {\n        services: [\n            {\n                name: \"Residential Relocation\",\n                href: \"/services/residential\"\n            },\n            {\n                name: \"Vehicle Transportation\",\n                href: \"/services/vehicle\"\n            },\n            {\n                name: \"Documentation Support\",\n                href: \"/services/documentation\"\n            },\n            {\n                name: \"Storage Solutions\",\n                href: \"/services/storage\"\n            }\n        ],\n        company: [\n            {\n                name: \"About Us\",\n                href: \"/about\"\n            },\n            {\n                name: \"How It Works\",\n                href: \"/how-it-works\"\n            },\n            {\n                name: \"Careers\",\n                href: \"/careers\"\n            },\n            {\n                name: \"Press\",\n                href: \"/press\"\n            }\n        ],\n        support: [\n            {\n                name: \"Contact Us\",\n                href: \"/contact\"\n            },\n            {\n                name: \"FAQ\",\n                href: \"/faq\"\n            },\n            {\n                name: \"Track Shipment\",\n                href: \"/track\"\n            },\n            {\n                name: \"Customer Portal\",\n                href: \"/portal\"\n            }\n        ],\n        legal: [\n            {\n                name: \"Privacy Policy\",\n                href: \"/privacy\"\n            },\n            {\n                name: \"Terms of Service\",\n                href: \"/terms\"\n            },\n            {\n                name: \"Cookie Policy\",\n                href: \"/cookies\"\n            },\n            {\n                name: \"Insurance\",\n                href: \"/insurance\"\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            name: \"Facebook\",\n            href: \"https://facebook.com/vayuvector\",\n            icon: \"facebook\"\n        },\n        {\n            name: \"Twitter\",\n            href: \"https://twitter.com/vayuvector\",\n            icon: \"twitter\"\n        },\n        {\n            name: \"LinkedIn\",\n            href: \"https://linkedin.com/company/vayuvector\",\n            icon: \"linkedin\"\n        },\n        {\n            name: \"Instagram\",\n            href: \"https://instagram.com/vayuvector\",\n            icon: \"instagram\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom py-12 lg:py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center space-x-2 mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            src: \"/logo-dark.svg\",\n                                            alt: \"VayuVector Logo\",\n                                            width: 160,\n                                            height: 48,\n                                            className: \"h-8 w-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-6 max-w-sm\",\n                                        children: \"Your trusted global relocation partner. Moving lives, not just belongings, with comprehensive door-to-door services worldwide.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.PhoneIcon, {\n                                                        className: \"h-5 w-5 text-accent-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"tel:******-VAYU-VEC\",\n                                                        className: \"text-gray-300 hover:text-white transition-colors\",\n                                                        children: \"******-VAYU-VEC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.EnvelopeIcon, {\n                                                        className: \"h-5 w-5 text-accent-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"mailto:<EMAIL>\",\n                                                        className: \"text-gray-300 hover:text-white transition-colors\",\n                                                        children: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.MapPinIcon, {\n                                                        className: \"h-5 w-5 text-accent-500 mt-0.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: [\n                                                            \"123 Logistics Ave\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 86,\n                                                                columnNumber: 36\n                                                            }, undefined),\n                                                            \"Global City, GC 12345\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ClockIcon, {\n                                                        className: \"h-5 w-5 text-accent-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"24/7 Customer Support\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Services\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.services.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.company.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Support\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.support.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Legal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.legal.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 pt-8 border-t border-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: \"Stay Updated\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-4\",\n                                    children: \"Get relocation tips and updates delivered to your inbox.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Enter your email\",\n                                            className: \"flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"btn-primary whitespace-nowrap\",\n                                            children: \"Subscribe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" VayuVector. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: social.href,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"text-gray-400 hover:text-white transition-colors duration-200\",\n                                        \"aria-label\": social.name,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: social.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-5 h-5 bg-gray-600 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, social.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Footer.tsx\n");

/***/ }),

/***/ "./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,PhoneIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=Bars3Icon,PhoneIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_heroicons_react_20_solid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon!=!@heroicons/react/20/solid */ \"__barrel_optimize__?names=ChevronDownIcon!=!./node_modules/@heroicons/react/20/solid/esm/index.js\");\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Home\",\n        href: \"/\"\n    },\n    {\n        name: \"Services\",\n        href: \"/services\",\n        children: [\n            {\n                name: \"Residential Relocation\",\n                href: \"/services/residential\"\n            },\n            {\n                name: \"Vehicle Transportation\",\n                href: \"/services/vehicle\"\n            },\n            {\n                name: \"Documentation Support\",\n                href: \"/services/documentation\"\n            }\n        ]\n    },\n    {\n        name: \"How It Works\",\n        href: \"/how-it-works\"\n    },\n    {\n        name: \"About\",\n        href: \"/about\"\n    },\n    {\n        name: \"Contact\",\n        href: \"/contact\"\n    }\n];\nconst Header = ()=>{\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [servicesOpen, setServicesOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const isScrolled = window.scrollY > 10;\n            setScrolled(isScrolled);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled ? \"bg-white/95 backdrop-blur-md shadow-soft\" : \"bg-transparent\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    src: \"/logo.svg\",\n                                    alt: \"VayuVector Logo\",\n                                    width: 200,\n                                    height: 60,\n                                    className: \"h-12 lg:h-14 w-auto\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: item.children ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: ()=>setServicesOpen(true),\n                                        onMouseLeave: ()=>setServicesOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `flex items-center space-x-1 px-3 py-2 text-sm font-medium transition-colors duration-200 ${isActive(item.href) ? \"text-secondary-600\" : scrolled ? \"text-gray-700 hover:text-secondary-600\" : \"text-white hover:text-secondary-200\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_heroicons_react_20_solid__WEBPACK_IMPORTED_MODULE_5__.ChevronDownIcon, {\n                                                        className: `h-4 w-4 transition-transform duration-200 ${servicesOpen ? \"rotate-180\" : \"\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            servicesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-medium border border-gray-100 py-2\",\n                                                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: child.href,\n                                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-secondary-600 transition-colors duration-200\",\n                                                        children: child.name\n                                                    }, child.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 27\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: `px-3 py-2 text-sm font-medium transition-colors duration-200 ${isActive(item.href) ? \"text-secondary-600\" : scrolled ? \"text-gray-700 hover:text-secondary-600\" : \"text-white hover:text-secondary-200\"}`,\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"tel:******-VAYU-VEC\",\n                                    className: `flex items-center space-x-2 px-3 py-2 text-sm font-medium transition-colors duration-200 ${scrolled ? \"text-gray-700 hover:text-secondary-600\" : \"text-white hover:text-secondary-200\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PhoneIcon, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"******-VAYU-VEC\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/quote\",\n                                    className: \"btn-secondary text-black font-semibold\",\n                                    children: \"Get Quote\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: `p-2 rounded-md transition-colors duration-200 ${scrolled ? \"text-gray-700 hover:text-secondary-600\" : \"text-white hover:text-secondary-200\"}`,\n                                onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open main menu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.XMarkIcon, {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.Bars3Icon, {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined),\n                mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 bg-white rounded-lg shadow-medium mt-2\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: `block px-3 py-2 text-base font-medium rounded-md transition-colors duration-200 ${isActive(item.href) ? \"text-secondary-600 bg-secondary-50\" : \"text-gray-700 hover:text-secondary-600 hover:bg-gray-50\"}`,\n                                            onClick: ()=>setMobileMenuOpen(false),\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 space-y-1\",\n                                            children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: child.href,\n                                                    className: \"block px-3 py-2 text-sm text-gray-600 hover:text-secondary-600 hover:bg-gray-50 rounded-md transition-colors duration-200\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: child.name\n                                                }, child.name, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 border-t border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"tel:******-VAYU-VEC\",\n                                        className: \"flex items-center space-x-2 px-3 py-2 text-base font-medium text-gray-700 hover:text-secondary-600 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PhoneIcon, {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"******-VAYU-VEC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/quote\",\n                                        className: \"block w-full mt-2 btn-secondary text-center text-black font-semibold\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Get Quote\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./components/Header.tsx\n");

/***/ }),

/***/ "./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Header */ \"./components/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Footer */ \"./components/Footer.tsx\");\n\n\n\n\n\nconst Layout = ({ children, title = \"VayuVector - Your Global Relocation Partner\", description = \"Professional international relocation services. Moving lives, not just belongings. Door-to-door service with comprehensive support.\", keywords = \"international moving, relocation services, global shipping, door-to-door moving, expat services, international logistics\", ogImage = \"/images/og-image.jpg\", noIndex = false })=>{\n    const fullTitle = title.includes(\"VayuVector\") ? title : `${title} | VayuVector`;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: fullTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: keywords\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: fullTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: ogImage\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:site_name\",\n                        content: \"VayuVector\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:title\",\n                        content: fullTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:image\",\n                        content: ogImage\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"canonical\",\n                        href: `https://vayuvector.com${ false ? 0 : \"\"}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined),\n                    noIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex, nofollow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"Organization\",\n                                \"name\": \"VayuVector\",\n                                \"description\": description,\n                                \"url\": \"https://vayuvector.com\",\n                                \"logo\": \"https://vayuvector.com/logo.svg\",\n                                \"contactPoint\": {\n                                    \"@type\": \"ContactPoint\",\n                                    \"telephone\": \"******-VAYU-VEC\",\n                                    \"contactType\": \"customer service\",\n                                    \"availableLanguage\": [\n                                        \"English\"\n                                    ]\n                                },\n                                \"sameAs\": [\n                                    \"https://facebook.com/vayuvector\",\n                                    \"https://twitter.com/vayuvector\",\n                                    \"https://linkedin.com/company/vayuvector\"\n                                ],\n                                \"address\": {\n                                    \"@type\": \"PostalAddress\",\n                                    \"streetAddress\": \"123 Logistics Ave\",\n                                    \"addressLocality\": \"Global City\",\n                                    \"addressRegion\": \"GC\",\n                                    \"postalCode\": \"12345\",\n                                    \"addressCountry\": \"US\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-grow\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./components/Layout.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_query_devtools__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query/devtools */ \"react-query/devtools\");\n/* harmony import */ var react_query_devtools__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_query_devtools__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_3__]);\nreact_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(()=>new react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    retry: 1,\n                    refetchOnWindowFocus: false,\n                    staleTime: 5 * 60 * 1000\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_app.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: \"#363636\",\n                        color: \"#fff\"\n                    },\n                    success: {\n                        duration: 3000,\n                        iconTheme: {\n                            primary: \"#10B981\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        duration: 5000,\n                        iconTheme: {\n                            primary: \"#EF4444\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_app.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query_devtools__WEBPACK_IMPORTED_MODULE_2__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_app.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_app.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preload\",\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap\",\n                        as: \"style\",\n                        onLoad: (e)=>{\n                            const target = e.target;\n                            target.onload = null;\n                            target.rel = \"stylesheet\";\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noscript\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            href: \"https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap\",\n                            rel: \"stylesheet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://api.vayuvector.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://www.google-analytics.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://maps.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#1E3A8A\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#1E3A8A\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./pages/how-it-works.tsx":
/*!********************************!*\
  !*** ./pages/how-it-works.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArchiveBoxIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,DocumentMagnifyingGlassIcon,GlobeAltIcon,HomeIcon,TruckIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArchiveBoxIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,DocumentMagnifyingGlassIcon,GlobeAltIcon,HomeIcon,TruckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_3__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nconst HowItWorksPage = ()=>{\n    const [activeStep, setActiveStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const steps = [\n        {\n            id: 1,\n            title: \"Initial Consultation\",\n            subtitle: \"Understanding Your Needs\",\n            description: \"We start with a comprehensive consultation to understand your specific moving requirements, timeline, and budget.\",\n            icon: _barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChatBubbleLeftRightIcon,\n            details: [\n                \"Free consultation call or video meeting\",\n                \"Assessment of your moving requirements\",\n                \"Discussion of timeline and preferences\",\n                \"Initial cost estimation\",\n                \"Custom moving plan development\"\n            ],\n            duration: \"30-60 minutes\",\n            color: \"primary\"\n        },\n        {\n            id: 2,\n            title: \"Detailed Survey\",\n            subtitle: \"Accurate Assessment\",\n            description: \"Our experts conduct a thorough survey of your belongings to provide the most accurate quote and moving plan.\",\n            icon: _barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.DocumentMagnifyingGlassIcon,\n            details: [\n                \"Virtual or in-home survey\",\n                \"Detailed inventory of belongings\",\n                \"Special items identification\",\n                \"Access and logistics assessment\",\n                \"Final quote preparation\"\n            ],\n            duration: \"1-2 hours\",\n            color: \"secondary\"\n        },\n        {\n            id: 3,\n            title: \"Professional Packing\",\n            subtitle: \"Expert Care\",\n            description: \"Our trained professionals carefully pack your belongings using high-quality materials and proven techniques.\",\n            icon: _barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ArchiveBoxIcon,\n            details: [\n                \"Professional packing team arrival\",\n                \"High-quality packing materials\",\n                \"Special handling for fragile items\",\n                \"Detailed inventory labeling\",\n                \"Quality control inspection\"\n            ],\n            duration: \"1-3 days\",\n            color: \"accent\"\n        },\n        {\n            id: 4,\n            title: \"Secure Transportation\",\n            subtitle: \"Safe Journey\",\n            description: \"Your belongings are transported using our secure network of trusted carriers with full tracking capabilities.\",\n            icon: _barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.TruckIcon,\n            details: [\n                \"Loading with care and precision\",\n                \"Secure transportation vehicles\",\n                \"Real-time tracking updates\",\n                \"Insurance coverage active\",\n                \"Regular status communications\"\n            ],\n            duration: \"1-6 weeks\",\n            color: \"green\"\n        },\n        {\n            id: 5,\n            title: \"Customs Clearance\",\n            subtitle: \"Smooth Processing\",\n            description: \"We handle all customs documentation and clearance procedures to ensure smooth entry into your destination country.\",\n            icon: _barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.GlobeAltIcon,\n            details: [\n                \"Complete documentation preparation\",\n                \"Customs declaration filing\",\n                \"Duty and tax calculation\",\n                \"Clearance coordination\",\n                \"Compliance verification\"\n            ],\n            duration: \"1-5 days\",\n            color: \"purple\"\n        },\n        {\n            id: 6,\n            title: \"Delivery & Setup\",\n            subtitle: \"Welcome Home\",\n            description: \"Final delivery to your new home with unpacking services and furniture assembly to get you settled quickly.\",\n            icon: _barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.HomeIcon,\n            details: [\n                \"Scheduled delivery coordination\",\n                \"Careful unloading and placement\",\n                \"Unpacking services\",\n                \"Furniture assembly\",\n                \"Final inspection and sign-off\"\n            ],\n            duration: \"1-2 days\",\n            color: \"indigo\"\n        }\n    ];\n    const timeline = [\n        {\n            phase: \"Planning Phase\",\n            duration: \"1-2 weeks\",\n            steps: [\n                1,\n                2\n            ]\n        },\n        {\n            phase: \"Packing Phase\",\n            duration: \"1-3 days\",\n            steps: [\n                3\n            ]\n        },\n        {\n            phase: \"Transit Phase\",\n            duration: \"1-6 weeks\",\n            steps: [\n                4,\n                5\n            ]\n        },\n        {\n            phase: \"Delivery Phase\",\n            duration: \"1-2 days\",\n            steps: [\n                6\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        title: \"How It Works - VayuVector\",\n        description: \"Learn about our comprehensive international moving process. From initial consultation to final delivery, we guide you through every step of your relocation journey.\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-20 pb-16 bg-gradient-to-br from-gray-50 to-secondary-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n                                children: \"How VayuVector Works\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg md:text-xl text-gray-600 mb-8\",\n                                children: \"Our proven 6-step process ensures your international move is handled with expertise, care, and transparency from start to finish.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CheckCircleIcon, {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700 font-medium\",\n                                                children: \"Proven Process\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ClockIcon, {\n                                                className: \"h-8 w-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700 font-medium\",\n                                                children: \"Transparent Timeline\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.GlobeAltIcon, {\n                                                className: \"h-8 w-8 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700 font-medium\",\n                                                children: \"Global Expertise\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Moving Timeline\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Typical timeline for an international move from start to finish.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mb-16\",\n                            children: timeline.map((phase, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-lg font-bold\",\n                                                    children: index + 1\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                index < timeline.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"hidden md:block absolute top-8 left-full w-full h-0.5 bg-gray-300 transform -translate-y-0.5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: phase.phase\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-2\",\n                                            children: phase.duration\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"Steps \",\n                                                phase.steps.join(\", \")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Detailed Process\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Click on each step to learn more about what happens during your move.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: steps.map((step, index)=>{\n                                        const IconComponent = step.icon;\n                                        const isActive = activeStep === index;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                            onClick: ()=>setActiveStep(index),\n                                            className: `w-full text-left p-6 rounded-xl transition-all duration-300 ${isActive ? \"bg-white shadow-medium border-l-4 border-primary-600\" : \"bg-white/50 hover:bg-white hover:shadow-soft\"}`,\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `flex-shrink-0 w-12 h-12 rounded-lg bg-${step.color}-100 flex items-center justify-center`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: `h-6 w-6 text-${step.color}-600`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-500\",\n                                                                        children: [\n                                                                            \"Step \",\n                                                                            step.id\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                        lineNumber: 234,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: step.duration\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: `text-lg font-semibold mb-1 ${isActive ? \"text-primary-600\" : \"text-gray-900\"}`,\n                                                                children: step.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: step.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, step.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:pl-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5\n                                        },\n                                        className: \"card p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `w-16 h-16 rounded-xl bg-${steps[activeStep].color}-100 flex items-center justify-center`,\n                                                        children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(steps[activeStep].icon, {\n                                                            className: `h-8 w-8 text-${steps[activeStep].color}-600`\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-500 mb-1\",\n                                                                children: [\n                                                                    \"Step \",\n                                                                    steps[activeStep].id,\n                                                                    \" • \",\n                                                                    steps[activeStep].duration\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                children: steps[activeStep].title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: steps[activeStep].subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 mb-6 leading-relaxed\",\n                                                children: steps[activeStep].description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900 mb-4\",\n                                                        children: \"What's Included:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    steps[activeStep].details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                            initial: {\n                                                                opacity: 0,\n                                                                x: -20\n                                                            },\n                                                            animate: {\n                                                                opacity: 1,\n                                                                x: 0\n                                                            },\n                                                            transition: {\n                                                                duration: 0.3,\n                                                                delay: index * 0.1\n                                                            },\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CheckCircleIcon, {\n                                                                    className: `h-5 w-5 text-${steps[activeStep].color}-600 flex-shrink-0`\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: detail\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, activeStep, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Quality Assurance\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Every step of our process includes quality checks to ensure your move exceeds expectations.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                {\n                                    title: \"Certified Professionals\",\n                                    description: \"All team members are trained and certified in international moving standards.\",\n                                    icon: \"\\uD83C\\uDFC6\"\n                                },\n                                {\n                                    title: \"Quality Checkpoints\",\n                                    description: \"Multiple quality control points throughout the moving process.\",\n                                    icon: \"✅\"\n                                },\n                                {\n                                    title: \"Customer Feedback\",\n                                    description: \"Continuous improvement based on customer feedback and satisfaction scores.\",\n                                    icon: \"⭐\"\n                                }\n                            ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-4\",\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: item.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section bg-primary-600 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4\",\n                            children: \"Ready to Experience Our Process?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg mb-8 text-primary-100 max-w-2xl mx-auto\",\n                            children: \"Start your international move with confidence. Get your free consultation and see how our proven process can make your relocation seamless.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn bg-white text-primary-600 hover:bg-gray-100 text-lg px-8 py-3\",\n                                    children: \"Start Free Consultation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn border-2 border-white text-white hover:bg-white hover:text-primary-600 text-lg px-8 py-3\",\n                                    children: \"Download Process Guide\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HowItWorksPage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/how-it-works.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-query":
/*!******************************!*\
  !*** external "react-query" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-query");

/***/ }),

/***/ "react-query/devtools":
/*!***************************************!*\
  !*** external "react-query/devtools" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-query/devtools");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "framer-motion":
/*!********************************!*\
  !*** external "framer-motion" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("framer-motion");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fhow-it-works&preferredRegion=&absolutePagePath=.%2Fpages%5Chow-it-works.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();