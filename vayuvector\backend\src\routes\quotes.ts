import express from 'express';
import { body } from 'express-validator';
import {
  createQuote,
  getQuotes,
  getQuote,
  updateQuote,
  deleteQuote,
  calculateQuote,
  saveDraft,
  emailQuote,
  getQuoteByNumber,
} from '../controllers/quotes';
import { protect, authorize } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';

const router = express.Router();

// Quote validation rules
const quoteValidation = [
  body('customer.firstName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  body('customer.lastName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),
  body('customer.email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('customer.phone')
    .matches(/^\+?[1-9]\d{1,14}$/)
    .withMessage('Please provide a valid phone number'),
  body('origin.country')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Origin country is required'),
  body('origin.city')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Origin city is required'),
  body('destination.country')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Destination country is required'),
  body('destination.city')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Destination city is required'),
  body('moveDetails.homeSize')
    .isIn(['studio', '1-bedroom', '2-bedroom', '3-bedroom', '4-bedroom', '5-bedroom', 'office'])
    .withMessage('Please select a valid home size'),
  body('moveDetails.moveDate')
    .isISO8601()
    .toDate()
    .withMessage('Please provide a valid move date'),
];

const calculateQuoteValidation = [
  body('origin.country').trim().isLength({ min: 1 }).withMessage('Origin country is required'),
  body('origin.city').trim().isLength({ min: 1 }).withMessage('Origin city is required'),
  body('destination.country').trim().isLength({ min: 1 }).withMessage('Destination country is required'),
  body('destination.city').trim().isLength({ min: 1 }).withMessage('Destination city is required'),
  body('homeSize')
    .isIn(['studio', '1-bedroom', '2-bedroom', '3-bedroom', '4-bedroom', '5-bedroom', 'office'])
    .withMessage('Please select a valid home size'),
  body('packingService').optional().isBoolean().withMessage('Packing service must be a boolean'),
  body('storageNeeded').optional().isBoolean().withMessage('Storage needed must be a boolean'),
  body('storageDuration').optional().isInt({ min: 1, max: 24 }).withMessage('Storage duration must be between 1 and 24 months'),
];

// Public routes
router.post('/calculate', calculateQuoteValidation, validateRequest, calculateQuote);
router.post('/save-draft', quoteValidation, validateRequest, saveDraft);
router.get('/public/:quoteNumber', getQuoteByNumber);

// Protected routes
router.use(protect);

router
  .route('/')
  .get(getQuotes)
  .post(quoteValidation, validateRequest, createQuote);

router
  .route('/:id')
  .get(getQuote)
  .put(authorize('admin', 'agent'), quoteValidation, validateRequest, updateQuote)
  .delete(authorize('admin'), deleteQuote);

// Email quote
router.post('/:id/email', emailQuote);

export default router;
