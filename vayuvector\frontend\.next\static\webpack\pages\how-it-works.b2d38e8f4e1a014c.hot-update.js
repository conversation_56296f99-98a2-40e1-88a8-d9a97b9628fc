"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/how-it-works",{

/***/ "./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,PhoneIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=Bars3Icon,PhoneIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_heroicons_react_20_solid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon!=!@heroicons/react/20/solid */ \"__barrel_optimize__?names=ChevronDownIcon!=!./node_modules/@heroicons/react/20/solid/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Home\",\n        href: \"/\"\n    },\n    {\n        name: \"Services\",\n        href: \"/services\",\n        children: [\n            {\n                name: \"Residential Relocation\",\n                href: \"/services/residential\"\n            },\n            {\n                name: \"Vehicle Transportation\",\n                href: \"/services/vehicle\"\n            },\n            {\n                name: \"Documentation Support\",\n                href: \"/services/documentation\"\n            }\n        ]\n    },\n    {\n        name: \"How It Works\",\n        href: \"/how-it-works\"\n    },\n    {\n        name: \"About\",\n        href: \"/about\"\n    },\n    {\n        name: \"Contact\",\n        href: \"/contact\"\n    }\n];\nconst Header = ()=>{\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [servicesOpen, setServicesOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const isScrolled = window.scrollY > 10;\n            setScrolled(isScrolled);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-300 \".concat(scrolled ? \"bg-white/95 backdrop-blur-md shadow-soft\" : \"bg-transparent\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    src: \"/logo.svg\",\n                                    alt: \"VayuVector Logo\",\n                                    width: 200,\n                                    height: 60,\n                                    className: \"h-12 lg:h-14 w-auto\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: item.children ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: ()=>setServicesOpen(true),\n                                        onMouseLeave: ()=>setServicesOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-1 px-3 py-2 text-sm font-medium transition-colors duration-200 \".concat(isActive(item.href) ? \"text-secondary-600\" : scrolled ? \"text-gray-700 hover:text-secondary-600\" : \"text-white hover:text-secondary-200\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_heroicons_react_20_solid__WEBPACK_IMPORTED_MODULE_5__.ChevronDownIcon, {\n                                                        className: \"h-4 w-4 transition-transform duration-200 \".concat(servicesOpen ? \"rotate-180\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            servicesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-medium border border-gray-100 py-2\",\n                                                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: child.href,\n                                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200\",\n                                                        children: child.name\n                                                    }, child.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 27\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: \"px-3 py-2 text-sm font-medium transition-colors duration-200 \".concat(isActive(item.href) ? \"text-primary-600\" : scrolled ? \"text-gray-700 hover:text-primary-600\" : \"text-white hover:text-primary-200\"),\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"tel:******-VAYU-VEC\",\n                                    className: \"flex items-center space-x-2 px-3 py-2 text-sm font-medium transition-colors duration-200 \".concat(scrolled ? \"text-gray-700 hover:text-primary-600\" : \"text-white hover:text-primary-200\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PhoneIcon, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"******-VAYU-VEC\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/quote\",\n                                    className: \"btn-primary\",\n                                    children: \"Get Quote\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"p-2 rounded-md transition-colors duration-200 \".concat(scrolled ? \"text-gray-700 hover:text-primary-600\" : \"text-white hover:text-primary-200\"),\n                                onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open main menu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.XMarkIcon, {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.Bars3Icon, {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined),\n                mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 bg-white rounded-lg shadow-medium mt-2\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: \"block px-3 py-2 text-base font-medium rounded-md transition-colors duration-200 \".concat(isActive(item.href) ? \"text-primary-600 bg-primary-50\" : \"text-gray-700 hover:text-primary-600 hover:bg-gray-50\"),\n                                            onClick: ()=>setMobileMenuOpen(false),\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 space-y-1\",\n                                            children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: child.href,\n                                                    className: \"block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: child.name\n                                                }, child.name, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 border-t border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"tel:******-VAYU-VEC\",\n                                        className: \"flex items-center space-x-2 px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_PhoneIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PhoneIcon, {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"******-VAYU-VEC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/quote\",\n                                        className: \"block w-full mt-2 btn-primary text-center\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: \"Get Quote\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Header, \"gFMnf2dRD7/9KwAbeSTM/4qKteo=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Header;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./components/Header.tsx\n"));

/***/ })

});