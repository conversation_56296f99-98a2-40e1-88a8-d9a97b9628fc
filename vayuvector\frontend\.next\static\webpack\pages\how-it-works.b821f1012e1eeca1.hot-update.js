"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/how-it-works",{

/***/ "./pages/how-it-works.tsx":
/*!********************************!*\
  !*** ./pages/how-it-works.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArchiveBoxIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,DocumentMagnifyingGlassIcon,GlobeAltIcon,HomeIcon,TruckIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArchiveBoxIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,DocumentMagnifyingGlassIcon,GlobeAltIcon,HomeIcon,TruckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst HowItWorksPage = ()=>{\n    _s();\n    const [activeStep, setActiveStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const steps = [\n        {\n            id: 1,\n            title: \"Initial Consultation\",\n            subtitle: \"Understanding Your Needs\",\n            description: \"We start with a comprehensive consultation to understand your specific moving requirements, timeline, and budget.\",\n            icon: _barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ChatBubbleLeftRightIcon,\n            details: [\n                \"Free consultation call or video meeting\",\n                \"Assessment of your moving requirements\",\n                \"Discussion of timeline and preferences\",\n                \"Initial cost estimation\",\n                \"Custom moving plan development\"\n            ],\n            duration: \"30-60 minutes\",\n            color: \"primary\"\n        },\n        {\n            id: 2,\n            title: \"Detailed Survey\",\n            subtitle: \"Accurate Assessment\",\n            description: \"Our experts conduct a thorough survey of your belongings to provide the most accurate quote and moving plan.\",\n            icon: _barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.DocumentMagnifyingGlassIcon,\n            details: [\n                \"Virtual or in-home survey\",\n                \"Detailed inventory of belongings\",\n                \"Special items identification\",\n                \"Access and logistics assessment\",\n                \"Final quote preparation\"\n            ],\n            duration: \"1-2 hours\",\n            color: \"secondary\"\n        },\n        {\n            id: 3,\n            title: \"Professional Packing\",\n            subtitle: \"Expert Care\",\n            description: \"Our trained professionals carefully pack your belongings using high-quality materials and proven techniques.\",\n            icon: _barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ArchiveBoxIcon,\n            details: [\n                \"Professional packing team arrival\",\n                \"High-quality packing materials\",\n                \"Special handling for fragile items\",\n                \"Detailed inventory labeling\",\n                \"Quality control inspection\"\n            ],\n            duration: \"1-3 days\",\n            color: \"accent\"\n        },\n        {\n            id: 4,\n            title: \"Secure Transportation\",\n            subtitle: \"Safe Journey\",\n            description: \"Your belongings are transported using our secure network of trusted carriers with full tracking capabilities.\",\n            icon: _barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.TruckIcon,\n            details: [\n                \"Loading with care and precision\",\n                \"Secure transportation vehicles\",\n                \"Real-time tracking updates\",\n                \"Insurance coverage active\",\n                \"Regular status communications\"\n            ],\n            duration: \"1-6 weeks\",\n            color: \"green\"\n        },\n        {\n            id: 5,\n            title: \"Customs Clearance\",\n            subtitle: \"Smooth Processing\",\n            description: \"We handle all customs documentation and clearance procedures to ensure smooth entry into your destination country.\",\n            icon: _barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.GlobeAltIcon,\n            details: [\n                \"Complete documentation preparation\",\n                \"Customs declaration filing\",\n                \"Duty and tax calculation\",\n                \"Clearance coordination\",\n                \"Compliance verification\"\n            ],\n            duration: \"1-5 days\",\n            color: \"purple\"\n        },\n        {\n            id: 6,\n            title: \"Delivery & Setup\",\n            subtitle: \"Welcome Home\",\n            description: \"Final delivery to your new home with unpacking services and furniture assembly to get you settled quickly.\",\n            icon: _barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.HomeIcon,\n            details: [\n                \"Scheduled delivery coordination\",\n                \"Careful unloading and placement\",\n                \"Unpacking services\",\n                \"Furniture assembly\",\n                \"Final inspection and sign-off\"\n            ],\n            duration: \"1-2 days\",\n            color: \"indigo\"\n        }\n    ];\n    const timeline = [\n        {\n            phase: \"Planning Phase\",\n            duration: \"1-2 weeks\",\n            steps: [\n                1,\n                2\n            ]\n        },\n        {\n            phase: \"Packing Phase\",\n            duration: \"1-3 days\",\n            steps: [\n                3\n            ]\n        },\n        {\n            phase: \"Transit Phase\",\n            duration: \"1-6 weeks\",\n            steps: [\n                4,\n                5\n            ]\n        },\n        {\n            phase: \"Delivery Phase\",\n            duration: \"1-2 days\",\n            steps: [\n                6\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        title: \"How It Works - VayuVector\",\n        description: \"Learn about our comprehensive international moving process. From initial consultation to final delivery, we guide you through every step of your relocation journey.\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-20 pb-16 bg-gradient-to-br from-gray-50 to-secondary-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n                                children: \"How VayuVector Works\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg md:text-xl text-gray-600 mb-8\",\n                                children: \"Our proven 6-step process ensures your international move is handled with expertise, care, and transparency from start to finish.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.CheckCircleIcon, {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700 font-medium\",\n                                                children: \"Proven Process\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ClockIcon, {\n                                                className: \"h-8 w-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700 font-medium\",\n                                                children: \"Transparent Timeline\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.GlobeAltIcon, {\n                                                className: \"h-8 w-8 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700 font-medium\",\n                                                children: \"Global Expertise\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Moving Timeline\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Typical timeline for an international move from start to finish.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mb-16\",\n                            children: timeline.map((phase, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-lg font-bold\",\n                                                    children: index + 1\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                index < timeline.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"hidden md:block absolute top-8 left-full w-full h-0.5 bg-gray-300 transform -translate-y-0.5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: phase.phase\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-2\",\n                                            children: phase.duration\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"Steps \",\n                                                phase.steps.join(\", \")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Detailed Process\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Click on each step to learn more about what happens during your move.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: steps.map((step, index)=>{\n                                        const IconComponent = step.icon;\n                                        const isActive = activeStep === index;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                            onClick: ()=>setActiveStep(index),\n                                            className: \"w-full text-left p-6 rounded-xl transition-all duration-300 \".concat(isActive ? \"bg-white shadow-medium border-l-4 border-primary-600\" : \"bg-white/50 hover:bg-white hover:shadow-soft\"),\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 w-12 h-12 rounded-lg bg-\".concat(step.color, \"-100 flex items-center justify-center\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"h-6 w-6 text-\".concat(step.color, \"-600\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-500\",\n                                                                        children: [\n                                                                            \"Step \",\n                                                                            step.id\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                        lineNumber: 234,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: step.duration\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold mb-1 \".concat(isActive ? \"text-primary-600\" : \"text-gray-900\"),\n                                                                children: step.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: step.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, step.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:pl-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5\n                                        },\n                                        className: \"card p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 rounded-xl bg-\".concat(steps[activeStep].color, \"-100 flex items-center justify-center\"),\n                                                        children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(steps[activeStep].icon, {\n                                                            className: \"h-8 w-8 text-\".concat(steps[activeStep].color, \"-600\")\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-500 mb-1\",\n                                                                children: [\n                                                                    \"Step \",\n                                                                    steps[activeStep].id,\n                                                                    \" • \",\n                                                                    steps[activeStep].duration\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                children: steps[activeStep].title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: steps[activeStep].subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 mb-6 leading-relaxed\",\n                                                children: steps[activeStep].description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900 mb-4\",\n                                                        children: \"What's Included:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    steps[activeStep].details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                            initial: {\n                                                                opacity: 0,\n                                                                x: -20\n                                                            },\n                                                            animate: {\n                                                                opacity: 1,\n                                                                x: 0\n                                                            },\n                                                            transition: {\n                                                                duration: 0.3,\n                                                                delay: index * 0.1\n                                                            },\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_DocumentMagnifyingGlassIcon_GlobeAltIcon_HomeIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.CheckCircleIcon, {\n                                                                    className: \"h-5 w-5 text-\".concat(steps[activeStep].color, \"-600 flex-shrink-0\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: detail\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, activeStep, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Quality Assurance\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Every step of our process includes quality checks to ensure your move exceeds expectations.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                {\n                                    title: \"Certified Professionals\",\n                                    description: \"All team members are trained and certified in international moving standards.\",\n                                    icon: \"\\uD83C\\uDFC6\"\n                                },\n                                {\n                                    title: \"Quality Checkpoints\",\n                                    description: \"Multiple quality control points throughout the moving process.\",\n                                    icon: \"✅\"\n                                },\n                                {\n                                    title: \"Customer Feedback\",\n                                    description: \"Continuous improvement based on customer feedback and satisfaction scores.\",\n                                    icon: \"⭐\"\n                                }\n                            ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-4\",\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: item.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section bg-primary-600 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4\",\n                            children: \"Ready to Experience Our Process?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg mb-8 text-primary-100 max-w-2xl mx-auto\",\n                            children: \"Start your international move with confidence. Get your free consultation and see how our proven process can make your relocation seamless.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn bg-white text-primary-600 hover:bg-gray-100 text-lg px-8 py-3\",\n                                    children: \"Start Free Consultation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn border-2 border-white text-white hover:bg-white hover:text-primary-600 text-lg px-8 py-3\",\n                                    children: \"Download Process Guide\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\how-it-works.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HowItWorksPage, \"cJXWosTT0XUh3gGn3cCcv/Y+Hws=\");\n_c = HowItWorksPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HowItWorksPage);\nvar _c;\n$RefreshReg$(_c, \"HowItWorksPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/how-it-works.tsx\n"));

/***/ })

});