import React, { useState } from 'react';
import Layout from '@/components/Layout';
import { motion } from 'framer-motion';
import { 
  ChatBubbleLeftRightIcon,
  DocumentMagnifyingGlassIcon,
  ArchiveBoxIcon,
  TruckIcon,
  GlobeAltIcon,
  HomeIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

const HowItWorksPage: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    {
      id: 1,
      title: 'Initial Consultation',
      subtitle: 'Understanding Your Needs',
      description: 'We start with a comprehensive consultation to understand your specific moving requirements, timeline, and budget.',
      icon: ChatBubbleLeftRightIcon,
      details: [
        'Free consultation call or video meeting',
        'Assessment of your moving requirements',
        'Discussion of timeline and preferences',
        'Initial cost estimation',
        'Custom moving plan development'
      ],
      duration: '30-60 minutes',
      color: 'primary',
    },
    {
      id: 2,
      title: 'Detailed Survey',
      subtitle: 'Accurate Assessment',
      description: 'Our experts conduct a thorough survey of your belongings to provide the most accurate quote and moving plan.',
      icon: DocumentMagnifyingGlassIcon,
      details: [
        'Virtual or in-home survey',
        'Detailed inventory of belongings',
        'Special items identification',
        'Access and logistics assessment',
        'Final quote preparation'
      ],
      duration: '1-2 hours',
      color: 'secondary',
    },
    {
      id: 3,
      title: 'Professional Packing',
      subtitle: 'Expert Care',
      description: 'Our trained professionals carefully pack your belongings using high-quality materials and proven techniques.',
      icon: ArchiveBoxIcon,
      details: [
        'Professional packing team arrival',
        'High-quality packing materials',
        'Special handling for fragile items',
        'Detailed inventory labeling',
        'Quality control inspection'
      ],
      duration: '1-3 days',
      color: 'accent',
    },
    {
      id: 4,
      title: 'Secure Transportation',
      subtitle: 'Safe Journey',
      description: 'Your belongings are transported using our secure network of trusted carriers with full tracking capabilities.',
      icon: TruckIcon,
      details: [
        'Loading with care and precision',
        'Secure transportation vehicles',
        'Real-time tracking updates',
        'Insurance coverage active',
        'Regular status communications'
      ],
      duration: '1-6 weeks',
      color: 'green',
    },
    {
      id: 5,
      title: 'Customs Clearance',
      subtitle: 'Smooth Processing',
      description: 'We handle all customs documentation and clearance procedures to ensure smooth entry into your destination country.',
      icon: GlobeAltIcon,
      details: [
        'Complete documentation preparation',
        'Customs declaration filing',
        'Duty and tax calculation',
        'Clearance coordination',
        'Compliance verification'
      ],
      duration: '1-5 days',
      color: 'purple',
    },
    {
      id: 6,
      title: 'Delivery & Setup',
      subtitle: 'Welcome Home',
      description: 'Final delivery to your new home with unpacking services and furniture assembly to get you settled quickly.',
      icon: HomeIcon,
      details: [
        'Scheduled delivery coordination',
        'Careful unloading and placement',
        'Unpacking services',
        'Furniture assembly',
        'Final inspection and sign-off'
      ],
      duration: '1-2 days',
      color: 'indigo',
    },
  ];

  const timeline = [
    { phase: 'Planning Phase', duration: '1-2 weeks', steps: [1, 2] },
    { phase: 'Packing Phase', duration: '1-3 days', steps: [3] },
    { phase: 'Transit Phase', duration: '1-6 weeks', steps: [4, 5] },
    { phase: 'Delivery Phase', duration: '1-2 days', steps: [6] },
  ];

  return (
    <Layout
      title="How It Works - VayuVector"
      description="Learn about our comprehensive international moving process. From initial consultation to final delivery, we guide you through every step of your relocation journey."
    >
      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-gray-50 to-secondary-50">
        <div className="container-custom">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              How VayuVector Works
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-8">
              Our proven 6-step process ensures your international move is handled 
              with expertise, care, and transparency from start to finish.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-center justify-center space-x-3">
                <CheckCircleIcon className="h-8 w-8 text-green-600" />
                <span className="text-gray-700 font-medium">Proven Process</span>
              </div>
              <div className="flex items-center justify-center space-x-3">
                <ClockIcon className="h-8 w-8 text-blue-600" />
                <span className="text-gray-700 font-medium">Transparent Timeline</span>
              </div>
              <div className="flex items-center justify-center space-x-3">
                <GlobeAltIcon className="h-8 w-8 text-purple-600" />
                <span className="text-gray-700 font-medium">Global Expertise</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Timeline Overview */}
      <section className="section">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Moving Timeline
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Typical timeline for an international move from start to finish.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
            {timeline.map((phase, index) => (
              <div key={index} className="text-center">
                <div className="relative">
                  <div className="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-lg font-bold">
                    {index + 1}
                  </div>
                  {index < timeline.length - 1 && (
                    <div className="hidden md:block absolute top-8 left-full w-full h-0.5 bg-gray-300 transform -translate-y-0.5"></div>
                  )}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {phase.phase}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  {phase.duration}
                </p>
                <div className="text-xs text-gray-500">
                  Steps {phase.steps.join(', ')}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Detailed Steps */}
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Detailed Process
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Click on each step to learn more about what happens during your move.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Step Navigation */}
            <div className="space-y-4">
              {steps.map((step, index) => {
                const IconComponent = step.icon;
                const isActive = activeStep === index;
                
                return (
                  <motion.button
                    key={step.id}
                    onClick={() => setActiveStep(index)}
                    className={`w-full text-left p-6 rounded-xl transition-all duration-300 ${
                      isActive 
                        ? 'bg-white shadow-medium border-l-4 border-primary-600' 
                        : 'bg-white/50 hover:bg-white hover:shadow-soft'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-start space-x-4">
                      <div className={`flex-shrink-0 w-12 h-12 rounded-lg bg-${step.color}-100 flex items-center justify-center`}>
                        <IconComponent className={`h-6 w-6 text-${step.color}-600`} />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="text-sm font-medium text-gray-500">
                            Step {step.id}
                          </span>
                          <span className="text-xs text-gray-400">
                            {step.duration}
                          </span>
                        </div>
                        <h3 className={`text-lg font-semibold mb-1 ${
                          isActive ? 'text-primary-600' : 'text-gray-900'
                        }`}>
                          {step.title}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {step.subtitle}
                        </p>
                      </div>
                    </div>
                  </motion.button>
                );
              })}
            </div>

            {/* Step Details */}
            <div className="lg:pl-8">
              <motion.div
                key={activeStep}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="card p-8"
              >
                <div className="flex items-center space-x-4 mb-6">
                  <div className={`w-16 h-16 rounded-xl bg-${steps[activeStep].color}-100 flex items-center justify-center`}>
                    {React.createElement(steps[activeStep].icon, {
                      className: `h-8 w-8 text-${steps[activeStep].color}-600`
                    })}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500 mb-1">
                      Step {steps[activeStep].id} • {steps[activeStep].duration}
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">
                      {steps[activeStep].title}
                    </h3>
                    <p className="text-gray-600">
                      {steps[activeStep].subtitle}
                    </p>
                  </div>
                </div>

                <p className="text-gray-700 mb-6 leading-relaxed">
                  {steps[activeStep].description}
                </p>

                <div className="space-y-3">
                  <h4 className="font-semibold text-gray-900 mb-4">
                    What's Included:
                  </h4>
                  {steps[activeStep].details.map((detail, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="flex items-center space-x-3"
                    >
                      <CheckCircleIcon className={`h-5 w-5 text-${steps[activeStep].color}-600 flex-shrink-0`} />
                      <span className="text-gray-700">{detail}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Quality Assurance */}
      <section className="section">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Quality Assurance
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Every step of our process includes quality checks to ensure your move exceeds expectations.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: 'Certified Professionals',
                description: 'All team members are trained and certified in international moving standards.',
                icon: '🏆',
              },
              {
                title: 'Quality Checkpoints',
                description: 'Multiple quality control points throughout the moving process.',
                icon: '✅',
              },
              {
                title: 'Customer Feedback',
                description: 'Continuous improvement based on customer feedback and satisfaction scores.',
                icon: '⭐',
              },
            ].map((item, index) => (
              <div key={index} className="card p-8 text-center">
                <div className="text-4xl mb-4">{item.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {item.title}
                </h3>
                <p className="text-gray-600">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section bg-primary-600 text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Experience Our Process?
          </h2>
          <p className="text-lg mb-8 text-primary-100 max-w-2xl mx-auto">
            Start your international move with confidence. Get your free consultation 
            and see how our proven process can make your relocation seamless.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn bg-white text-primary-600 hover:bg-gray-100 text-lg px-8 py-3">
              Start Free Consultation
            </button>
            <button className="btn border-2 border-white text-white hover:bg-white hover:text-primary-600 text-lg px-8 py-3">
              Download Process Guide
            </button>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default HowItWorksPage;
