"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.tsx\");\n/* harmony import */ var _components_QuoteCalculator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/QuoteCalculator */ \"./components/QuoteCalculator.tsx\");\n/* harmony import */ var _components_ServicesCarousel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ServicesCarousel */ \"./components/ServicesCarousel.tsx\");\n/* harmony import */ var _components_Testimonials__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Testimonials */ \"./components/Testimonials.tsx\");\n/* harmony import */ var _components_WorldMap__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/WorldMap */ \"./components/WorldMap.tsx\");\n\n\n\n\n\n\n\n\nconst HomePage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"VayuVector - Your Global Relocation Partner\",\n        description: \"Professional international relocation services. Moving lives, not just belongings. Door-to-door service with comprehensive support for expats and professionals worldwide.\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-900 via-primary-800 to-secondary-600 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-hero-pattern\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/4 w-64 h-64 bg-secondary-500/20 rounded-full blur-3xl animate-float\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent-500/20 rounded-full blur-3xl animate-float\",\n                                style: {\n                                    animationDelay: \"1s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 container-custom text-center text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 animate-fade-in-up\",\n                                    children: [\n                                        \"Your Global\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-secondary-500\",\n                                            children: \"Relocation Partner\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl lg:text-3xl mb-8 text-gray-200 animate-fade-in-up\",\n                                    style: {\n                                        animationDelay: \"0.2s\"\n                                    },\n                                    children: \"Moving Lives, Not Just Belongings\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg md:text-xl mb-12 text-gray-300 max-w-3xl mx-auto animate-fade-in-up\",\n                                    style: {\n                                        animationDelay: \"0.4s\"\n                                    },\n                                    children: \"Professional door-to-door relocation services for international professionals. From packing to destination setup, we handle every detail of your global move.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center items-center animate-fade-in-up\",\n                                    style: {\n                                        animationDelay: \"0.6s\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/quote\",\n                                            className: \"btn-secondary text-lg px-8 py-4 shadow-glow-accent hover:shadow-glow-accent text-black font-semibold\",\n                                            children: \"Get Free Quote\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/how-it-works\",\n                                            className: \"btn-outline text-lg px-8 py-4 border-secondary-500 text-secondary-500 hover:bg-secondary-500 hover:text-black\",\n                                            children: \"How It Works\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 animate-fade-in-up\",\n                                    style: {\n                                        animationDelay: \"0.8s\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl md:text-4xl font-bold text-secondary-400 mb-2\",\n                                                    children: \"15+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Years Experience\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl md:text-4xl font-bold text-secondary-400 mb-2\",\n                                                    children: \"50K+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Successful Moves\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl md:text-4xl font-bold text-secondary-400 mb-2\",\n                                                    children: \"120+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Countries Served\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Get Your Moving Quote in Minutes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600\",\n                                    children: \"Tell us about your move and get an instant estimate\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuoteCalculator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ServicesCarousel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WorldMap__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Testimonials__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n_c = HomePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ })

});