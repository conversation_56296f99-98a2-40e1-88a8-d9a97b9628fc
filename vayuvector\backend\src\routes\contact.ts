import express, { Request, Response } from 'express';
import { body } from 'express-validator';
import { validateRequest } from '../middleware/validation';

const router = express.Router();

// Contact form submission
router.post('/', [
  body('name').trim().isLength({ min: 1 }).withMessage('Name is required'),
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('phone').optional().matches(/^\+?[1-9]\d{1,14}$/).withMessage('Valid phone number required'),
  body('subject').trim().isLength({ min: 1 }).withMessage('Subject is required'),
  body('message').trim().isLength({ min: 10 }).withMessage('Message must be at least 10 characters'),
], validateRequest, (_req: Request, res: Response) => {
  // TODO: Implement contact form handling
  res.status(200).json({
    success: true,
    message: 'Contact form submitted successfully',
  });
});

export default router;
